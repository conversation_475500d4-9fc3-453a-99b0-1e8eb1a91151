// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'farm_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FarmModel _$FarmModelFromJson(Map<String, dynamic> json) => FarmModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      ownerId: json['ownerId'] as String,
      location: FarmLocation.fromJson(json['location'] as Map<String, dynamic>),
      area: (json['area'] as num).toDouble(),
      areaUnit: json['areaUnit'] as String? ?? 'hectares',
      farmType: $enumDecode(_$FarmTypeEnumMap, json['farmType']),
      crops: (json['crops'] as List<dynamic>?)
              ?.map((e) => CropInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      soilInfo: json['soilInfo'] == null
          ? null
          : SoilInfo.fromJson(json['soilInfo'] as Map<String, dynamic>),
      weatherInfo: json['weatherInfo'] == null
          ? null
          : WeatherInfo.fromJson(json['weatherInfo'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
      imageUrls: (json['imageUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$FarmModelToJson(FarmModel instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'ownerId': instance.ownerId,
      'location': instance.location,
      'area': instance.area,
      'areaUnit': instance.areaUnit,
      'farmType': _$FarmTypeEnumMap[instance.farmType]!,
      'crops': instance.crops,
      'soilInfo': instance.soilInfo,
      'weatherInfo': instance.weatherInfo,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isActive': instance.isActive,
      'imageUrls': instance.imageUrls,
    };

const _$FarmTypeEnumMap = {
  FarmType.organic: 'organic',
  FarmType.conventional: 'conventional',
  FarmType.mixed: 'mixed',
};

FarmLocation _$FarmLocationFromJson(Map<String, dynamic> json) => FarmLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      country: json['country'] as String,
      postalCode: json['postalCode'] as String?,
    );

Map<String, dynamic> _$FarmLocationToJson(FarmLocation instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'postalCode': instance.postalCode,
    };

CropInfo _$CropInfoFromJson(Map<String, dynamic> json) => CropInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      variety: json['variety'] as String,
      type: $enumDecode(_$CropTypeEnumMap, json['type']),
      plantingDate: DateTime.parse(json['plantingDate'] as String),
      harvestDate: json['harvestDate'] == null
          ? null
          : DateTime.parse(json['harvestDate'] as String),
      stage: $enumDecode(_$CropStageEnumMap, json['stage']),
      areaAllocated: (json['areaAllocated'] as num).toDouble(),
      imageUrls: (json['imageUrls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      health: $enumDecode(_$CropHealthEnumMap, json['health']),
    );

Map<String, dynamic> _$CropInfoToJson(CropInfo instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'variety': instance.variety,
      'type': _$CropTypeEnumMap[instance.type]!,
      'plantingDate': instance.plantingDate.toIso8601String(),
      'harvestDate': instance.harvestDate?.toIso8601String(),
      'stage': _$CropStageEnumMap[instance.stage]!,
      'areaAllocated': instance.areaAllocated,
      'imageUrls': instance.imageUrls,
      'health': _$CropHealthEnumMap[instance.health]!,
    };

const _$CropTypeEnumMap = {
  CropType.cereal: 'cereal',
  CropType.vegetable: 'vegetable',
  CropType.fruit: 'fruit',
  CropType.legume: 'legume',
  CropType.cashCrop: 'cash_crop',
};

const _$CropStageEnumMap = {
  CropStage.seedling: 'seedling',
  CropStage.vegetative: 'vegetative',
  CropStage.flowering: 'flowering',
  CropStage.fruiting: 'fruiting',
  CropStage.maturity: 'maturity',
  CropStage.harvested: 'harvested',
};

const _$CropHealthEnumMap = {
  CropHealth.excellent: 'excellent',
  CropHealth.good: 'good',
  CropHealth.fair: 'fair',
  CropHealth.poor: 'poor',
  CropHealth.critical: 'critical',
};

SoilInfo _$SoilInfoFromJson(Map<String, dynamic> json) => SoilInfo(
      id: json['id'] as String,
      ph: (json['ph'] as num).toDouble(),
      nitrogen: (json['nitrogen'] as num).toDouble(),
      phosphorus: (json['phosphorus'] as num).toDouble(),
      potassium: (json['potassium'] as num).toDouble(),
      organicMatter: (json['organicMatter'] as num).toDouble(),
      soilType: $enumDecode(_$SoilTypeEnumMap, json['soilType']),
      health: $enumDecode(_$SoilHealthEnumMap, json['health']),
      lastTested: DateTime.parse(json['lastTested'] as String),
      reportUrl: json['reportUrl'] as String?,
    );

Map<String, dynamic> _$SoilInfoToJson(SoilInfo instance) => <String, dynamic>{
      'id': instance.id,
      'ph': instance.ph,
      'nitrogen': instance.nitrogen,
      'phosphorus': instance.phosphorus,
      'potassium': instance.potassium,
      'organicMatter': instance.organicMatter,
      'soilType': _$SoilTypeEnumMap[instance.soilType]!,
      'health': _$SoilHealthEnumMap[instance.health]!,
      'lastTested': instance.lastTested.toIso8601String(),
      'reportUrl': instance.reportUrl,
    };

const _$SoilTypeEnumMap = {
  SoilType.clay: 'clay',
  SoilType.sandy: 'sandy',
  SoilType.loam: 'loam',
  SoilType.silt: 'silt',
  SoilType.peat: 'peat',
};

const _$SoilHealthEnumMap = {
  SoilHealth.excellent: 'excellent',
  SoilHealth.good: 'good',
  SoilHealth.fair: 'fair',
  SoilHealth.poor: 'poor',
};

WeatherInfo _$WeatherInfoFromJson(Map<String, dynamic> json) => WeatherInfo(
      temperature: (json['temperature'] as num).toDouble(),
      humidity: (json['humidity'] as num).toDouble(),
      rainfall: (json['rainfall'] as num).toDouble(),
      windSpeed: (json['windSpeed'] as num).toDouble(),
      condition: json['condition'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$WeatherInfoToJson(WeatherInfo instance) =>
    <String, dynamic>{
      'temperature': instance.temperature,
      'humidity': instance.humidity,
      'rainfall': instance.rainfall,
      'windSpeed': instance.windSpeed,
      'condition': instance.condition,
      'timestamp': instance.timestamp.toIso8601String(),
    };
