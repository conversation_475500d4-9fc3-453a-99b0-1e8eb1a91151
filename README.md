# Farm Management App

This is a Flutter/Dart project for farm management. The app includes:

- User authentication
- Dashboard
- Soil health card upload
- Leaf color chart scanning
- Fertilizer and pesticide recommendations
- GPS location services

## Project Structure

- `lib/main.dart`: App entry point
- `lib/models/`: Data models
- `lib/screens/`: UI screens
- `lib/services/`: API, Auth, Location services
- `lib/utils/`: Constants and helpers
- `lib/widgets/`: Reusable UI components

## Getting Started

1. Open the project in VS Code.
2. Run `flutter pub get` to install dependencies.
3. Use `flutter run` to launch the app.

---

For custom Copilot instructions, see `.github/copilot-instructions.md`.
