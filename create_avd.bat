@echo off
echo Creating Android Virtual Device...

REM Create AVD directory
mkdir "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd" 2>nul

REM Create config.ini file
echo avd.ini.displayname=Pixel 7 API 34 > "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo avd.ini.encoding=UTF-8 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo AvdId=Pixel_7_API_34 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo PlayStore.enabled=false >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo abi.type=x86_64 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo avd.ini.displayname=Pixel 7 API 34 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo disk.dataPartition.size=6442450944 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo fastboot.chosenSnapshotFile= >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo fastboot.forceChosenSnapshotBoot=no >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo fastboot.forceColdBoot=no >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo fastboot.forceFastBoot=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.accelerometer=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.arc=false >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.audioInput=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.battery=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.camera.back=virtualscene >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.camera.front=emulated >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.cpu.arch=x86_64 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.cpu.ncore=4 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.dPad=no >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.device.hash2=MD5:4e47c5d9ac1b4e0c3a0b4c8b8b8b8b8b >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.device.manufacturer=Google >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.device.name=pixel_7 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.gps=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.gpu.enabled=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.gpu.mode=auto >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.initialOrientation=Portrait >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.keyboard=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.lcd.density=420 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.lcd.height=2400 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.lcd.width=1080 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.mainKeys=no >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.ramSize=1536 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.sdCard=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.sensors.orientation=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.sensors.proximity=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo hw.trackBall=no >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo image.sysdir.1=system-images\android-34\google_apis\x86_64\ >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo runtime.network.latency=none >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo runtime.network.speed=full >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo skin.dynamic=yes >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo skin.name=pixel_7 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo skin.path=skins\pixel_7 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo tag.display=Google APIs >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo tag.id=google_apis >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"
echo vm.heapSize=256 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.avd\config.ini"

REM Create AVD ini file
echo avd.ini.displayname=Pixel 7 API 34 > "%USERPROFILE%\.android\avd\Pixel_7_API_34.ini"
echo avd.ini.encoding=UTF-8 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.ini"
echo path=%USERPROFILE%\.android\avd\Pixel_7_API_34.avd >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.ini"
echo path.rel=avd\Pixel_7_API_34.avd >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.ini"
echo target=android-34 >> "%USERPROFILE%\.android\avd\Pixel_7_API_34.ini"

echo AVD created successfully!
echo You can now start it with: emulator -avd Pixel_7_API_34
