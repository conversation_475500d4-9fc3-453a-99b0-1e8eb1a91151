import 'package:json_annotation/json_annotation.dart';

part 'recommendation_model.g.dart';

@JsonSerializable()
class RecommendationModel {
  final String id;
  final String farmId;
  final String cropId;
  final RecommendationType type;
  final String title;
  final String description;
  final RecommendationPriority priority;
  final List<RecommendationAction> actions;
  final DateTime createdAt;
  final DateTime? implementedAt;
  final RecommendationStatus status;
  final String? sourceType; // 'soil_analysis', 'leaf_scan', 'weather', 'expert'
  final Map<String, dynamic>? metadata;

  const RecommendationModel({
    required this.id,
    required this.farmId,
    required this.cropId,
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.actions,
    required this.createdAt,
    this.implementedAt,
    this.status = RecommendationStatus.pending,
    this.sourceType,
    this.metadata,
  });

  factory RecommendationModel.fromJson(Map<String, dynamic> json) =>
      _$RecommendationModelFromJson(json);

  Map<String, dynamic> toJson() => _$RecommendationModelToJson(this);

  bool get isImplemented => implementedAt != null;
  bool get isPending => status == RecommendationStatus.pending;
  bool get isActive => status == RecommendationStatus.active;

  int get daysOld => DateTime.now().difference(createdAt).inDays;

  RecommendationModel copyWith({
    String? id,
    String? farmId,
    String? cropId,
    RecommendationType? type,
    String? title,
    String? description,
    RecommendationPriority? priority,
    List<RecommendationAction>? actions,
    DateTime? createdAt,
    DateTime? implementedAt,
    RecommendationStatus? status,
    String? sourceType,
    Map<String, dynamic>? metadata,
  }) {
    return RecommendationModel(
      id: id ?? this.id,
      farmId: farmId ?? this.farmId,
      cropId: cropId ?? this.cropId,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      actions: actions ?? this.actions,
      createdAt: createdAt ?? this.createdAt,
      implementedAt: implementedAt ?? this.implementedAt,
      status: status ?? this.status,
      sourceType: sourceType ?? this.sourceType,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class RecommendationAction {
  final String id;
  final String title;
  final String description;
  final ActionType type;
  final Map<String, dynamic> parameters;
  final DateTime? scheduledDate;
  final bool isCompleted;
  final DateTime? completedAt;
  final String? notes;

  const RecommendationAction({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.parameters,
    this.scheduledDate,
    this.isCompleted = false,
    this.completedAt,
    this.notes,
  });

  factory RecommendationAction.fromJson(Map<String, dynamic> json) =>
      _$RecommendationActionFromJson(json);

  Map<String, dynamic> toJson() => _$RecommendationActionToJson(this);

  bool get isScheduled => scheduledDate != null;
  bool get isOverdue => 
      scheduledDate != null && 
      DateTime.now().isAfter(scheduledDate!) && 
      !isCompleted;

  int? get daysUntilScheduled {
    if (scheduledDate == null) return null;
    final diff = scheduledDate!.difference(DateTime.now()).inDays;
    return diff > 0 ? diff : 0;
  }
}

// Enums
enum RecommendationType {
  @JsonValue('fertilizer')
  fertilizer,
  @JsonValue('pesticide')
  pesticide,
  @JsonValue('irrigation')
  irrigation,
  @JsonValue('soil_amendment')
  soilAmendment,
  @JsonValue('crop_management')
  cropManagement,
  @JsonValue('disease_control')
  diseaseControl,
  @JsonValue('pest_control')
  pestControl,
  @JsonValue('harvest')
  harvest,
}

enum RecommendationPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

enum RecommendationStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('active')
  active,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('expired')
  expired,
}

enum ActionType {
  @JsonValue('apply_fertilizer')
  applyFertilizer,
  @JsonValue('apply_pesticide')
  applyPesticide,
  @JsonValue('irrigate')
  irrigate,
  @JsonValue('soil_test')
  soilTest,
  @JsonValue('plant')
  plant,
  @JsonValue('harvest')
  harvest,
  @JsonValue('prune')
  prune,
  @JsonValue('monitor')
  monitor,
}

// Extensions
extension RecommendationTypeExtension on RecommendationType {
  String get displayName {
    switch (this) {
      case RecommendationType.fertilizer:
        return 'Fertilizer';
      case RecommendationType.pesticide:
        return 'Pesticide';
      case RecommendationType.irrigation:
        return 'Irrigation';
      case RecommendationType.soilAmendment:
        return 'Soil Amendment';
      case RecommendationType.cropManagement:
        return 'Crop Management';
      case RecommendationType.diseaseControl:
        return 'Disease Control';
      case RecommendationType.pestControl:
        return 'Pest Control';
      case RecommendationType.harvest:
        return 'Harvest';
    }
  }

  String get iconName {
    switch (this) {
      case RecommendationType.fertilizer:
        return 'fertilizer';
      case RecommendationType.pesticide:
        return 'spray';
      case RecommendationType.irrigation:
        return 'water_drop';
      case RecommendationType.soilAmendment:
        return 'landscape';
      case RecommendationType.cropManagement:
        return 'agriculture';
      case RecommendationType.diseaseControl:
        return 'healing';
      case RecommendationType.pestControl:
        return 'bug_report';
      case RecommendationType.harvest:
        return 'grass';
    }
  }
}

extension RecommendationPriorityExtension on RecommendationPriority {
  String get displayName {
    switch (this) {
      case RecommendationPriority.low:
        return 'Low';
      case RecommendationPriority.medium:
        return 'Medium';
      case RecommendationPriority.high:
        return 'High';
      case RecommendationPriority.critical:
        return 'Critical';
    }
  }

  String get colorHex {
    switch (this) {
      case RecommendationPriority.low:
        return '#4CAF50';
      case RecommendationPriority.medium:
        return '#FF9800';
      case RecommendationPriority.high:
        return '#F44336';
      case RecommendationPriority.critical:
        return '#D32F2F';
    }
  }
}

extension ActionTypeExtension on ActionType {
  String get displayName {
    switch (this) {
      case ActionType.applyFertilizer:
        return 'Apply Fertilizer';
      case ActionType.applyPesticide:
        return 'Apply Pesticide';
      case ActionType.irrigate:
        return 'Irrigate';
      case ActionType.soilTest:
        return 'Soil Test';
      case ActionType.plant:
        return 'Plant';
      case ActionType.harvest:
        return 'Harvest';
      case ActionType.prune:
        return 'Prune';
      case ActionType.monitor:
        return 'Monitor';
    }
  }
}
