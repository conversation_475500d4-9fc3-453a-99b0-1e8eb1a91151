import 'package:json_annotation/json_annotation.dart';

part 'soil_model.g.dart';

// Soil data model
@JsonSerializable()
class SoilModel {
  final String id;
  final String farmId;
  final String sampleLocation;
  final DateTime collectionDate;
  final DateTime? analysisDate;
  final SoilTestResults testResults;
  final SoilRecommendations recommendations;
  final String? reportImageUrl;
  final String? labName;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SoilModel({
    required this.id,
    required this.farmId,
    required this.sampleLocation,
    required this.collectionDate,
    this.analysisDate,
    required this.testResults,
    required this.recommendations,
    this.reportImageUrl,
    this.labName,
    this.status = 'pending',
    required this.createdAt,
    required this.updatedAt,
  });

  factory SoilModel.fromJson(Map<String, dynamic> json) =>
      _$SoilModelFromJson(json);

  Map<String, dynamic> toJson() => _$SoilModelToJson(this);

  bool get isAnalyzed => analysisDate != null && status == 'completed';

  int get daysSinceCollection {
    return DateTime.now().difference(collectionDate).inDays;
  }

  SoilHealthGrade get overallHealthGrade {
    if (!isAnalyzed) return SoilHealthGrade.unknown;

    final scores = [
      testResults.phLevel.healthScore,
      testResults.nitrogen.healthScore,
      testResults.phosphorus.healthScore,
      testResults.potassium.healthScore,
      testResults.organicMatter.healthScore,
    ];

    final averageScore = scores.reduce((a, b) => a + b) / scores.length;

    if (averageScore >= 80) return SoilHealthGrade.excellent;
    if (averageScore >= 65) return SoilHealthGrade.good;
    if (averageScore >= 50) return SoilHealthGrade.fair;
    return SoilHealthGrade.poor;
  }

  SoilModel copyWith({
    String? id,
    String? farmId,
    String? sampleLocation,
    DateTime? collectionDate,
    DateTime? analysisDate,
    SoilTestResults? testResults,
    SoilRecommendations? recommendations,
    String? reportImageUrl,
    String? labName,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SoilModel(
      id: id ?? this.id,
      farmId: farmId ?? this.farmId,
      sampleLocation: sampleLocation ?? this.sampleLocation,
      collectionDate: collectionDate ?? this.collectionDate,
      analysisDate: analysisDate ?? this.analysisDate,
      testResults: testResults ?? this.testResults,
      recommendations: recommendations ?? this.recommendations,
      reportImageUrl: reportImageUrl ?? this.reportImageUrl,
      labName: labName ?? this.labName,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SoilModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SoilModel(id: $id, farmId: $farmId, status: $status)';
  }
}

@JsonSerializable()
class SoilTestResults {
  final SoilParameter phLevel;
  final SoilParameter nitrogen;
  final SoilParameter phosphorus;
  final SoilParameter potassium;
  final SoilParameter organicMatter;
  final SoilParameter? calcium;
  final SoilParameter? magnesium;
  final SoilParameter? sulfur;
  final SoilParameter? zinc;
  final SoilParameter? iron;
  final SoilParameter? manganese;
  final SoilParameter? copper;
  final SoilParameter? boron;
  final double? electricalConductivity;
  final String? soilTexture;

  const SoilTestResults({
    required this.phLevel,
    required this.nitrogen,
    required this.phosphorus,
    required this.potassium,
    required this.organicMatter,
    this.calcium,
    this.magnesium,
    this.sulfur,
    this.zinc,
    this.iron,
    this.manganese,
    this.copper,
    this.boron,
    this.electricalConductivity,
    this.soilTexture,
  });

  factory SoilTestResults.fromJson(Map<String, dynamic> json) =>
      _$SoilTestResultsFromJson(json);

  Map<String, dynamic> toJson() => _$SoilTestResultsToJson(this);

  List<SoilParameter> get allParameters {
    return [
      phLevel,
      nitrogen,
      phosphorus,
      potassium,
      organicMatter,
      if (calcium != null) calcium!,
      if (magnesium != null) magnesium!,
      if (sulfur != null) sulfur!,
      if (zinc != null) zinc!,
      if (iron != null) iron!,
      if (manganese != null) manganese!,
      if (copper != null) copper!,
      if (boron != null) boron!,
    ];
  }

  List<SoilParameter> get deficientParameters {
    return allParameters.where((param) => param.isDeficient).toList();
  }

  List<SoilParameter> get excessiveParameters {
    return allParameters.where((param) => param.isExcessive).toList();
  }
}

@JsonSerializable()
class SoilParameter {
  final String name;
  final double value;
  final String unit;
  final double optimalMin;
  final double optimalMax;
  final SoilParameterStatus status;
  final String? interpretation;

  const SoilParameter({
    required this.name,
    required this.value,
    required this.unit,
    required this.optimalMin,
    required this.optimalMax,
    required this.status,
    this.interpretation,
  });

  factory SoilParameter.fromJson(Map<String, dynamic> json) =>
      _$SoilParameterFromJson(json);

  Map<String, dynamic> toJson() => _$SoilParameterToJson(this);

  bool get isOptimal => status == SoilParameterStatus.optimal;
  bool get isDeficient => status == SoilParameterStatus.deficient;
  bool get isExcessive => status == SoilParameterStatus.excessive;

  double get healthScore {
    if (isOptimal) return 100.0;
    if (isDeficient) {
      final ratio = value / optimalMin;
      return (ratio * 60).clamp(0.0, 60.0);
    }
    if (isExcessive) {
      final ratio = optimalMax / value;
      return (ratio * 60).clamp(0.0, 60.0);
    }
    return 50.0;
  }

  String get displayValue => '$value $unit';

  String get statusDescription {
    switch (status) {
      case SoilParameterStatus.deficient:
        return 'Below optimal range';
      case SoilParameterStatus.optimal:
        return 'Within optimal range';
      case SoilParameterStatus.excessive:
        return 'Above optimal range';
    }
  }

  SoilParameter copyWith({
    String? name,
    double? value,
    String? unit,
    double? optimalMin,
    double? optimalMax,
    SoilParameterStatus? status,
    String? interpretation,
  }) {
    return SoilParameter(
      name: name ?? this.name,
      value: value ?? this.value,
      unit: unit ?? this.unit,
      optimalMin: optimalMin ?? this.optimalMin,
      optimalMax: optimalMax ?? this.optimalMax,
      status: status ?? this.status,
      interpretation: interpretation ?? this.interpretation,
    );
  }
}

@JsonSerializable()
class SoilRecommendations {
  final List<FertilizerRecommendation> fertilizers;
  final List<AmendmentRecommendation> amendments;
  final List<String> generalAdvice;
  final CropSuitability cropSuitability;
  final DateTime nextTestingDate;

  const SoilRecommendations({
    required this.fertilizers,
    required this.amendments,
    required this.generalAdvice,
    required this.cropSuitability,
    required this.nextTestingDate,
  });

  factory SoilRecommendations.fromJson(Map<String, dynamic> json) =>
      _$SoilRecommendationsFromJson(json);

  Map<String, dynamic> toJson() => _$SoilRecommendationsToJson(this);

  bool get hasFertilizerRecommendations => fertilizers.isNotEmpty;
  bool get hasAmendmentRecommendations => amendments.isNotEmpty;

  int get daysUntilNextTest {
    return nextTestingDate.difference(DateTime.now()).inDays;
  }
}

@JsonSerializable()
class FertilizerRecommendation {
  final String name;
  final String type;
  final double quantity;
  final String unit;
  final String applicationMethod;
  final String timing;
  final String purpose;
  final double? costEstimate;

  const FertilizerRecommendation({
    required this.name,
    required this.type,
    required this.quantity,
    required this.unit,
    required this.applicationMethod,
    required this.timing,
    required this.purpose,
    this.costEstimate,
  });

  factory FertilizerRecommendation.fromJson(Map<String, dynamic> json) =>
      _$FertilizerRecommendationFromJson(json);

  Map<String, dynamic> toJson() => _$FertilizerRecommendationToJson(this);

  String get displayQuantity => '$quantity $unit';
}

@JsonSerializable()
class AmendmentRecommendation {
  final String name;
  final String purpose;
  final double quantity;
  final String unit;
  final String applicationMethod;
  final String timing;
  final double? costEstimate;

  const AmendmentRecommendation({
    required this.name,
    required this.purpose,
    required this.quantity,
    required this.unit,
    required this.applicationMethod,
    required this.timing,
    this.costEstimate,
  });

  factory AmendmentRecommendation.fromJson(Map<String, dynamic> json) =>
      _$AmendmentRecommendationFromJson(json);

  Map<String, dynamic> toJson() => _$AmendmentRecommendationToJson(this);

  String get displayQuantity => '$quantity $unit';
}

@JsonSerializable()
class CropSuitability {
  final List<SuitableCrop> suitableCrops;
  final List<SuitableCrop> moderatelySuitableCrops;
  final List<SuitableCrop> unsuitableCrops;

  const CropSuitability({
    required this.suitableCrops,
    required this.moderatelySuitableCrops,
    required this.unsuitableCrops,
  });

  factory CropSuitability.fromJson(Map<String, dynamic> json) =>
      _$CropSuitabilityFromJson(json);

  Map<String, dynamic> toJson() => _$CropSuitabilityToJson(this);
}

@JsonSerializable()
class SuitableCrop {
  final String name;
  final String variety;
  final double suitabilityScore;
  final String reason;
  final List<String> requirements;

  const SuitableCrop({
    required this.name,
    required this.variety,
    required this.suitabilityScore,
    required this.reason,
    required this.requirements,
  });

  factory SuitableCrop.fromJson(Map<String, dynamic> json) =>
      _$SuitableCropFromJson(json);

  Map<String, dynamic> toJson() => _$SuitableCropToJson(this);

  String get suitabilityPercentage => '${(suitabilityScore * 100).toInt()}%';
}

// Enums
enum SoilHealthGrade {
  @JsonValue('excellent')
  excellent,
  @JsonValue('good')
  good,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
  @JsonValue('unknown')
  unknown,
}

enum SoilParameterStatus {
  @JsonValue('deficient')
  deficient,
  @JsonValue('optimal')
  optimal,
  @JsonValue('excessive')
  excessive,
}

// Extensions
extension SoilHealthGradeExtension on SoilHealthGrade {
  String get displayName {
    switch (this) {
      case SoilHealthGrade.excellent:
        return 'Excellent';
      case SoilHealthGrade.good:
        return 'Good';
      case SoilHealthGrade.fair:
        return 'Fair';
      case SoilHealthGrade.poor:
        return 'Poor';
      case SoilHealthGrade.unknown:
        return 'Unknown';
    }
  }

  String get description {
    switch (this) {
      case SoilHealthGrade.excellent:
        return 'Soil is in excellent condition with optimal nutrient levels';
      case SoilHealthGrade.good:
        return 'Soil is in good condition with minor adjustments needed';
      case SoilHealthGrade.fair:
        return 'Soil needs moderate improvement in nutrient levels';
      case SoilHealthGrade.poor:
        return 'Soil requires significant improvement and amendments';
      case SoilHealthGrade.unknown:
        return 'Soil analysis is pending or incomplete';
    }
  }

  String get colorHex {
    switch (this) {
      case SoilHealthGrade.excellent:
        return '#4CAF50';
      case SoilHealthGrade.good:
        return '#8BC34A';
      case SoilHealthGrade.fair:
        return '#FF9800';
      case SoilHealthGrade.poor:
        return '#F44336';
      case SoilHealthGrade.unknown:
        return '#9E9E9E';
    }
  }
}

extension SoilParameterStatusExtension on SoilParameterStatus {
  String get displayName {
    switch (this) {
      case SoilParameterStatus.deficient:
        return 'Deficient';
      case SoilParameterStatus.optimal:
        return 'Optimal';
      case SoilParameterStatus.excessive:
        return 'Excessive';
    }
  }

  String get colorHex {
    switch (this) {
      case SoilParameterStatus.deficient:
        return '#F44336';
      case SoilParameterStatus.optimal:
        return '#4CAF50';
      case SoilParameterStatus.excessive:
        return '#FF9800';
    }
  }
}
