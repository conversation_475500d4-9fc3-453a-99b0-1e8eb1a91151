from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
import uuid
import os
from datetime import datetime

from ..database import get_db
from ..auth import get_current_active_user
from ..models import User, Farm, LeafScan
from ..schemas import LeafScanCreate, LeafScanUpdate, LeafScanResponse
from ..config import settings

router = APIRouter()

@router.post("/upload", response_model=LeafScanResponse)
async def upload_leaf_image(
    farm_id: str,
    file: UploadFile = File(...),
    crop_id: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload leaf image for analysis"""
    # Verify farm ownership
    farm = db.query(Farm).filter(Farm.id == farm_id).first()
    if not farm:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Farm not found"
        )
    
    if farm.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Validate file
    if file.content_type not in ["image/jpeg", "image/png", "image/jpg"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only JPEG and PNG images are allowed"
        )
    
    try:
        # Save uploaded file
        file_extension = os.path.splitext(file.filename)[1]
        filename = f"leaf_{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.UPLOAD_DIR, filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Create leaf scan record
        leaf_scan_id = str(uuid.uuid4())
        db_leaf_scan = LeafScan(
            id=leaf_scan_id,
            user_id=current_user.id,
            farm_id=farm_id,
            crop_id=crop_id,
            image_url=file_path,
            scan_date=datetime.utcnow(),
            status="processing"
        )
        
        db.add(db_leaf_scan)
        db.commit()
        db.refresh(db_leaf_scan)
        
        # TODO: Trigger background task for ML analysis
        # For now, simulate analysis with mock data
        await simulate_leaf_analysis(db_leaf_scan, db)
        
        return LeafScanResponse.from_orm(db_leaf_scan)
        
    except Exception as e:
        db.rollback()
        # Clean up uploaded file if database operation fails
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload leaf image: {str(e)}"
        )

async def simulate_leaf_analysis(leaf_scan: LeafScan, db: Session):
    """Simulate leaf analysis with mock data"""
    try:
        # Mock analysis results
        leaf_scan.status = "completed"
        leaf_scan.confidence = 0.92
        leaf_scan.overall_health = "healthy"
        leaf_scan.color_analysis = {
            "dominant_color": "#4CAF50",
            "greenness": 0.75,
            "yellowness": 0.15,
            "browning": 0.10,
            "health_indicator": "healthy"
        }
        leaf_scan.diseases_detected = []
        leaf_scan.deficiencies_detected = []
        leaf_scan.pests_detected = []
        leaf_scan.physical_condition = {
            "leaf_area": 25.5,
            "spot_coverage": 0.05,
            "wilting": 0.0,
            "damage": 0.05,
            "grade": "good"
        }
        leaf_scan.recommendations = [
            {
                "title": "Continue current care",
                "description": "Leaf appears healthy, maintain current practices",
                "type": "monitoring",
                "urgency": "low"
            }
        ]
        
        db.commit()
        db.refresh(leaf_scan)
        
    except Exception as e:
        print(f"Error in leaf analysis simulation: {e}")

@router.get("/", response_model=List[LeafScanResponse])
async def list_leaf_scans(
    farm_id: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get leaf scans for user's farms"""
    query = db.query(LeafScan).filter(LeafScan.user_id == current_user.id)
    
    if farm_id:
        # Verify farm ownership
        farm = db.query(Farm).filter(Farm.id == farm_id).first()
        if not farm or farm.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        query = query.filter(LeafScan.farm_id == farm_id)
    
    leaf_scans = query.order_by(LeafScan.created_at.desc()).all()
    return [LeafScanResponse.from_orm(scan) for scan in leaf_scans]

@router.get("/{scan_id}", response_model=LeafScanResponse)
async def get_leaf_scan(
    scan_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get leaf scan by ID"""
    leaf_scan = db.query(LeafScan).filter(LeafScan.id == scan_id).first()
    
    if not leaf_scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf scan not found"
        )
    
    # Check ownership
    if leaf_scan.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return LeafScanResponse.from_orm(leaf_scan)

@router.put("/{scan_id}", response_model=LeafScanResponse)
async def update_leaf_scan(
    scan_id: str,
    scan_update: LeafScanUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update leaf scan (for experts/admins)"""
    leaf_scan = db.query(LeafScan).filter(LeafScan.id == scan_id).first()
    
    if not leaf_scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf scan not found"
        )
    
    # Check permissions (owner or expert/admin)
    if (leaf_scan.user_id != current_user.id and 
        current_user.role not in ["expert", "admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Update leaf scan fields
        update_data = scan_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(leaf_scan, field):
                setattr(leaf_scan, field, value)
        
        db.commit()
        db.refresh(leaf_scan)
        
        return LeafScanResponse.from_orm(leaf_scan)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update leaf scan: {str(e)}"
        )

@router.delete("/{scan_id}")
async def delete_leaf_scan(
    scan_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete leaf scan"""
    leaf_scan = db.query(LeafScan).filter(LeafScan.id == scan_id).first()
    
    if not leaf_scan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leaf scan not found"
        )
    
    # Check ownership
    if leaf_scan.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Delete associated file
        if leaf_scan.image_url and os.path.exists(leaf_scan.image_url):
            os.remove(leaf_scan.image_url)
        
        db.delete(leaf_scan)
        db.commit()
        
        return {"message": "Leaf scan deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete leaf scan: {str(e)}"
        )
