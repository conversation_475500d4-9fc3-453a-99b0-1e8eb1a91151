# Farm Management Python Backend

A comprehensive FastAPI-based backend for the Farm Management application with SQL database support.

## Features

- 🔐 **JWT Authentication** - Secure user authentication and authorization
- 🗄️ **SQL Database** - PostgreSQL/SQLite support with SQLAlchemy ORM
- 🌱 **Farm Management** - Create and manage multiple farms
- 🧪 **Soil Analysis** - Upload soil health cards and get analysis
- 🍃 **Leaf Scanning** - AI-powered leaf health analysis
- 💡 **Recommendations** - Smart farming recommendations
- 📊 **RESTful API** - Well-documented REST endpoints
- 🚀 **FastAPI** - High-performance async API framework

## Quick Start

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. **Clone and navigate to the backend directory:**
   ```bash
   cd python-backend
   ```

2. **Run the startup script (Windows):**
   ```bash
   start_server.bat
   ```

   **Or manually (Linux/Mac):**
   ```bash
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate.bat  # Windows
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Start server
   python run.py
   ```

3. **Access the API:**
   - Server: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

## Database Configuration

### SQLite (Default - Development)
The backend uses SQLite by default for easy development setup. No additional configuration needed.

### PostgreSQL (Production)
To use PostgreSQL:

1. Install PostgreSQL
2. Create a database named `farm_management`
3. Update `.env` file:
   ```env
   USE_SQLITE=false
   DATABASE_URL=postgresql://username:password@localhost:5432/farm_management
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/refresh` - Refresh token

### Users
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update user profile

### Farms
- `GET /api/farms/` - List user's farms
- `POST /api/farms/` - Create new farm
- `GET /api/farms/{farm_id}` - Get farm details
- `PUT /api/farms/{farm_id}` - Update farm
- `DELETE /api/farms/{farm_id}` - Delete farm

### Soil Analysis
- `POST /api/soil/upload` - Upload soil health card
- `GET /api/soil/` - List soil tests
- `GET /api/soil/{test_id}` - Get soil test details

### Leaf Scanning
- `POST /api/leaf-scan/upload` - Upload leaf image
- `GET /api/leaf-scan/` - List leaf scans
- `GET /api/leaf-scan/{scan_id}` - Get scan details

### Recommendations
- `GET /api/recommendations/` - List recommendations
- `POST /api/recommendations/` - Create recommendation
- `PUT /api/recommendations/{id}` - Update recommendation

## Testing

Run the test script to verify the backend is working:

```bash
python test_backend.py
```

## Configuration

Edit the `.env` file to configure:

- Database connection
- JWT secret key
- File upload settings
- External API keys
- Email settings

## Development

### Project Structure
```
python-backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI app
│   ├── config.py            # Configuration
│   ├── database.py          # Database setup
│   ├── models.py            # SQLAlchemy models
│   ├── schemas.py           # Pydantic schemas
│   ├── auth.py              # Authentication utilities
│   └── routers/             # API route handlers
│       ├── auth.py
│       ├── users.py
│       ├── farms.py
│       ├── soil.py
│       ├── leaf_scan.py
│       └── recommendations.py
├── uploads/                 # File uploads
├── requirements.txt         # Dependencies
├── .env                     # Environment variables
├── run.py                   # Server startup
└── README.md
```

### Adding New Features

1. **Add database model** in `app/models.py`
2. **Create Pydantic schemas** in `app/schemas.py`
3. **Implement API routes** in `app/routers/`
4. **Register router** in `main.py`

## Production Deployment

1. Set `DEBUG=false` in `.env`
2. Use PostgreSQL database
3. Set strong `SECRET_KEY`
4. Configure proper CORS origins
5. Use a production WSGI server like Gunicorn
6. Set up SSL/HTTPS
7. Configure file storage (AWS S3, etc.)

## Security Features

- JWT token authentication
- Password hashing with bcrypt
- Request rate limiting
- CORS protection
- Input validation
- SQL injection prevention
- File upload validation

## Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review the test script output
3. Check server logs for errors
4. Verify database connection
