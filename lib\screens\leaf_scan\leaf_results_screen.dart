import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../models/leaf_scan_model.dart';
import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../widgets/custom_button.dart';

class LeafResultsScreen extends ConsumerWidget {
  final LeafScanModel leafScan;

  const LeafResultsScreen({
    super.key,
    required this.leafScan,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Leaf Analysis Results'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              AppHelpers.showInfoSnackBar(context, 'Share functionality coming soon');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image and Overall Health
            _buildImageAndHealthCard(context),
            
            const SizedBox(height: AppDimensions.paddingLarge),
            
            // Color Analysis
            _buildColorAnalysisCard(context),
            
            const SizedBox(height: AppDimensions.paddingLarge),
            
            // Physical Condition
            _buildPhysicalConditionCard(context),
            
            const SizedBox(height: AppDimensions.paddingLarge),
            
            // Issues Detection
            _buildIssuesCard(context),
            
            const SizedBox(height: AppDimensions.paddingLarge),
            
            // Recommendations
            _buildRecommendationsCard(context),
            
            const SizedBox(height: AppDimensions.paddingLarge),
            
            // Actions
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildImageAndHealthCard(BuildContext context) {
    final healthGrade = leafScan.overallHealth;
    
    return Card(
      child: Column(
        children: [
          // Leaf Image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppDimensions.radiusMedium),
            ),
            child: Image.file(
              File(leafScan.imageUrl),
              height: 200,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          
          // Health Information
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingLarge),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                      decoration: BoxDecoration(
                        color: _getHealthColor(healthGrade).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                      ),
                      child: Icon(
                        _getHealthIcon(healthGrade),
                        size: AppDimensions.iconLarge,
                        color: _getHealthColor(healthGrade),
                      ),
                    ),
                    const SizedBox(width: AppDimensions.paddingMedium),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Leaf Health Status',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            _getHealthDisplayName(healthGrade),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: _getHealthColor(healthGrade),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.paddingMedium),
                Row(
                  children: [
                    Icon(Icons.psychology, size: 16, color: AppColors.textSecondary),
                    const SizedBox(width: 4),
                    Text(
                      'Confidence: ${AppHelpers.formatPercentage(leafScan.confidence ?? 0.0)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    Icon(Icons.calendar_today, size: 16, color: AppColors.textSecondary),
                    const SizedBox(width: 4),
                    Text(
                      AppHelpers.formatDate(leafScan.scanDate),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorAnalysisCard(BuildContext context) {
    final colorAnalysis = leafScan.analysisResults.colorAnalysis;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Color Analysis',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingMedium),
            
            // Color Distribution
            Row(
              children: [
                Expanded(
                  child: _buildColorMetric(
                    context,
                    'Greenness',
                    colorAnalysis.greenness,
                    AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildColorMetric(
                    context,
                    'Yellowness',
                    colorAnalysis.yellowness,
                    AppColors.warning,
                  ),
                ),
                Expanded(
                  child: _buildColorMetric(
                    context,
                    'Browning',
                    colorAnalysis.browning,
                    AppColors.error,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingMedium),
            
            // Health Indicator
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingMedium),
              decoration: BoxDecoration(
                color: _getColorHealthColor(colorAnalysis.healthIndicator).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.palette,
                    color: _getColorHealthColor(colorAnalysis.healthIndicator),
                  ),
                  const SizedBox(width: AppDimensions.paddingSmall),
                  Text(
                    'Color Health: ${_getColorHealthDisplayName(colorAnalysis.healthIndicator)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: _getColorHealthColor(colorAnalysis.healthIndicator),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorMetric(BuildContext context, String label, double value, Color color) {
    return Column(
      children: [
        Text(
          AppHelpers.formatPercentage(value),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildPhysicalConditionCard(BuildContext context) {
    final condition = leafScan.analysisResults.physicalCondition;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Physical Condition',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildConditionMetric(
                    context,
                    'Leaf Area',
                    '${condition.leafArea.toStringAsFixed(1)} cm²',
                  ),
                ),
                Expanded(
                  child: _buildConditionMetric(
                    context,
                    'Spot Coverage',
                    AppHelpers.formatPercentage(condition.spotCoverage),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppDimensions.paddingMedium),
            
            Row(
              children: [
                Expanded(
                  child: _buildConditionMetric(
                    context,
                    'Wilting',
                    AppHelpers.formatPercentage(condition.wilting),
                  ),
                ),
                Expanded(
                  child: _buildConditionMetric(
                    context,
                    'Damage',
                    AppHelpers.formatPercentage(condition.damage),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConditionMetric(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildIssuesCard(BuildContext context) {
    final results = leafScan.analysisResults;
    final hasIssues = results.diseases.isNotEmpty || 
                     results.deficiencies.isNotEmpty || 
                     results.pests.isNotEmpty;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Issues Detected',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingMedium),
            
            if (!hasIssues) ...[
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 48,
                      color: AppColors.success.withOpacity(0.7),
                    ),
                    const SizedBox(height: AppDimensions.paddingSmall),
                    Text(
                      'No issues detected',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'Your leaf appears healthy!',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              // Display detected issues
              if (results.diseases.isNotEmpty) ...[
                _buildIssueSection(context, 'Diseases', results.diseases.length, AppColors.error),
              ],
              if (results.deficiencies.isNotEmpty) ...[
                _buildIssueSection(context, 'Deficiencies', results.deficiencies.length, AppColors.warning),
              ],
              if (results.pests.isNotEmpty) ...[
                _buildIssueSection(context, 'Pests', results.pests.length, AppColors.accent),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIssueSection(BuildContext context, String title, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingSmall),
      child: Row(
        children: [
          Icon(Icons.warning, color: color, size: 20),
          const SizedBox(width: AppDimensions.paddingSmall),
          Text(
            '$title: $count detected',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recommendations',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingMedium),
            
            if (leafScan.recommendations.isEmpty) ...[
              Text(
                'Continue regular monitoring and maintain current care practices.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ] else ...[
              // Display recommendations
              ...leafScan.recommendations.map(
                (rec) => Padding(
                  padding: const EdgeInsets.only(bottom: AppDimensions.paddingSmall),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.check_circle, color: AppColors.success, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          rec.title,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Column(
      children: [
        PrimaryButton(
          text: 'Scan Another Leaf',
          icon: Icons.camera_alt,
          onPressed: () => context.go('/leaf-scan'),
        ),
        const SizedBox(height: AppDimensions.paddingMedium),
        SecondaryButton(
          text: 'View History',
          icon: Icons.history,
          onPressed: () {
            AppHelpers.showInfoSnackBar(context, 'History feature coming soon');
          },
        ),
      ],
    );
  }

  // Helper methods for health status
  Color _getHealthColor(LeafHealthGrade grade) {
    switch (grade) {
      case LeafHealthGrade.healthy:
        return AppColors.success;
      case LeafHealthGrade.good:
        return AppColors.primary;
      case LeafHealthGrade.fair:
        return AppColors.warning;
      case LeafHealthGrade.poor:
        return AppColors.error;
      case LeafHealthGrade.critical:
        return AppColors.error;
      case LeafHealthGrade.unknown:
        return AppColors.textSecondary;
    }
  }

  IconData _getHealthIcon(LeafHealthGrade grade) {
    switch (grade) {
      case LeafHealthGrade.healthy:
        return Icons.eco;
      case LeafHealthGrade.good:
        return Icons.eco;
      case LeafHealthGrade.fair:
        return Icons.warning;
      case LeafHealthGrade.poor:
        return Icons.error;
      case LeafHealthGrade.critical:
        return Icons.dangerous;
      case LeafHealthGrade.unknown:
        return Icons.help;
    }
  }

  String _getHealthDisplayName(LeafHealthGrade grade) {
    switch (grade) {
      case LeafHealthGrade.healthy:
        return 'Healthy';
      case LeafHealthGrade.good:
        return 'Good';
      case LeafHealthGrade.fair:
        return 'Fair';
      case LeafHealthGrade.poor:
        return 'Poor';
      case LeafHealthGrade.critical:
        return 'Critical';
      case LeafHealthGrade.unknown:
        return 'Unknown';
    }
  }

  Color _getColorHealthColor(ColorHealthIndicator indicator) {
    switch (indicator) {
      case ColorHealthIndicator.healthy:
        return AppColors.success;
      case ColorHealthIndicator.stressed:
        return AppColors.warning;
      case ColorHealthIndicator.deficient:
        return AppColors.accent;
      case ColorHealthIndicator.diseased:
        return AppColors.error;
    }
  }

  String _getColorHealthDisplayName(ColorHealthIndicator indicator) {
    switch (indicator) {
      case ColorHealthIndicator.healthy:
        return 'Healthy';
      case ColorHealthIndicator.stressed:
        return 'Stressed';
      case ColorHealthIndicator.deficient:
        return 'Deficient';
      case ColorHealthIndicator.diseased:
        return 'Diseased';
    }
  }
}
