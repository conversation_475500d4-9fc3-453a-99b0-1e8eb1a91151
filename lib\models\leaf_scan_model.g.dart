// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaf_scan_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeafScanModel _$LeafScanModelFromJson(Map<String, dynamic> json) =>
    LeafScanModel(
      id: json['id'] as String,
      farmId: json['farmId'] as String,
      cropId: json['cropId'] as String,
      imageUrl: json['imageUrl'] as String,
      scanDate: DateTime.parse(json['scanDate'] as String),
      analysisResults: LeafAnalysisResults.fromJson(
          json['analysisResults'] as Map<String, dynamic>),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => LeafRecommendation.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecodeNullable(_$ScanStatusEnumMap, json['status']) ??
          ScanStatus.processing,
      confidence: (json['confidence'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$LeafScanModelToJson(LeafScanModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'farmId': instance.farmId,
      'cropId': instance.cropId,
      'imageUrl': instance.imageUrl,
      'scanDate': instance.scanDate.toIso8601String(),
      'analysisResults': instance.analysisResults,
      'recommendations': instance.recommendations,
      'status': _$ScanStatusEnumMap[instance.status]!,
      'confidence': instance.confidence,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ScanStatusEnumMap = {
  ScanStatus.processing: 'processing',
  ScanStatus.completed: 'completed',
  ScanStatus.failed: 'failed',
};

LeafAnalysisResults _$LeafAnalysisResultsFromJson(Map<String, dynamic> json) =>
    LeafAnalysisResults(
      colorAnalysis: LeafColorAnalysis.fromJson(
          json['colorAnalysis'] as Map<String, dynamic>),
      diseases: (json['diseases'] as List<dynamic>)
          .map((e) => DiseaseDetection.fromJson(e as Map<String, dynamic>))
          .toList(),
      deficiencies: (json['deficiencies'] as List<dynamic>)
          .map((e) => NutrientDeficiency.fromJson(e as Map<String, dynamic>))
          .toList(),
      pests: (json['pests'] as List<dynamic>)
          .map((e) => PestDetection.fromJson(e as Map<String, dynamic>))
          .toList(),
      physicalCondition: LeafPhysicalCondition.fromJson(
          json['physicalCondition'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LeafAnalysisResultsToJson(
        LeafAnalysisResults instance) =>
    <String, dynamic>{
      'colorAnalysis': instance.colorAnalysis,
      'diseases': instance.diseases,
      'deficiencies': instance.deficiencies,
      'pests': instance.pests,
      'physicalCondition': instance.physicalCondition,
    };

LeafColorAnalysis _$LeafColorAnalysisFromJson(Map<String, dynamic> json) =>
    LeafColorAnalysis(
      dominantColor: json['dominantColor'] as String,
      greenness: (json['greenness'] as num).toDouble(),
      yellowness: (json['yellowness'] as num).toDouble(),
      browning: (json['browning'] as num).toDouble(),
      colorDistribution:
          (json['colorDistribution'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      healthIndicator:
          $enumDecode(_$ColorHealthIndicatorEnumMap, json['healthIndicator']),
    );

Map<String, dynamic> _$LeafColorAnalysisToJson(LeafColorAnalysis instance) =>
    <String, dynamic>{
      'dominantColor': instance.dominantColor,
      'greenness': instance.greenness,
      'yellowness': instance.yellowness,
      'browning': instance.browning,
      'colorDistribution': instance.colorDistribution,
      'healthIndicator':
          _$ColorHealthIndicatorEnumMap[instance.healthIndicator]!,
    };

const _$ColorHealthIndicatorEnumMap = {
  ColorHealthIndicator.healthy: 'healthy',
  ColorHealthIndicator.stressed: 'stressed',
  ColorHealthIndicator.deficient: 'deficient',
  ColorHealthIndicator.diseased: 'diseased',
};

DiseaseDetection _$DiseaseDetectionFromJson(Map<String, dynamic> json) =>
    DiseaseDetection(
      id: json['id'] as String,
      name: json['name'] as String,
      scientificName: json['scientificName'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      severity: $enumDecode(_$DiseaseSeverityEnumMap, json['severity']),
      description: json['description'] as String,
      symptoms:
          (json['symptoms'] as List<dynamic>).map((e) => e as String).toList(),
      causes:
          (json['causes'] as List<dynamic>).map((e) => e as String).toList(),
      treatments: (json['treatments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$DiseaseDetectionToJson(DiseaseDetection instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'scientificName': instance.scientificName,
      'confidence': instance.confidence,
      'severity': _$DiseaseSeverityEnumMap[instance.severity]!,
      'description': instance.description,
      'symptoms': instance.symptoms,
      'causes': instance.causes,
      'treatments': instance.treatments,
    };

const _$DiseaseSeverityEnumMap = {
  DiseaseSeverity.mild: 'mild',
  DiseaseSeverity.moderate: 'moderate',
  DiseaseSeverity.severe: 'severe',
  DiseaseSeverity.critical: 'critical',
};

NutrientDeficiency _$NutrientDeficiencyFromJson(Map<String, dynamic> json) =>
    NutrientDeficiency(
      nutrient: json['nutrient'] as String,
      name: json['name'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      severity: $enumDecode(_$DeficiencySeverityEnumMap, json['severity']),
      description: json['description'] as String,
      symptoms:
          (json['symptoms'] as List<dynamic>).map((e) => e as String).toList(),
      solutions:
          (json['solutions'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$NutrientDeficiencyToJson(NutrientDeficiency instance) =>
    <String, dynamic>{
      'nutrient': instance.nutrient,
      'name': instance.name,
      'confidence': instance.confidence,
      'severity': _$DeficiencySeverityEnumMap[instance.severity]!,
      'description': instance.description,
      'symptoms': instance.symptoms,
      'solutions': instance.solutions,
    };

const _$DeficiencySeverityEnumMap = {
  DeficiencySeverity.mild: 'mild',
  DeficiencySeverity.moderate: 'moderate',
  DeficiencySeverity.severe: 'severe',
};

PestDetection _$PestDetectionFromJson(Map<String, dynamic> json) =>
    PestDetection(
      id: json['id'] as String,
      name: json['name'] as String,
      scientificName: json['scientificName'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      severity: $enumDecode(_$PestSeverityEnumMap, json['severity']),
      description: json['description'] as String,
      symptoms:
          (json['symptoms'] as List<dynamic>).map((e) => e as String).toList(),
      treatments: (json['treatments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$PestDetectionToJson(PestDetection instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'scientificName': instance.scientificName,
      'confidence': instance.confidence,
      'severity': _$PestSeverityEnumMap[instance.severity]!,
      'description': instance.description,
      'symptoms': instance.symptoms,
      'treatments': instance.treatments,
    };

const _$PestSeverityEnumMap = {
  PestSeverity.low: 'low',
  PestSeverity.medium: 'medium',
  PestSeverity.high: 'high',
  PestSeverity.critical: 'critical',
};

LeafPhysicalCondition _$LeafPhysicalConditionFromJson(
        Map<String, dynamic> json) =>
    LeafPhysicalCondition(
      leafArea: (json['leafArea'] as num).toDouble(),
      spotCoverage: (json['spotCoverage'] as num).toDouble(),
      wilting: (json['wilting'] as num).toDouble(),
      damage: (json['damage'] as num).toDouble(),
      grade: $enumDecode(_$PhysicalConditionGradeEnumMap, json['grade']),
    );

Map<String, dynamic> _$LeafPhysicalConditionToJson(
        LeafPhysicalCondition instance) =>
    <String, dynamic>{
      'leafArea': instance.leafArea,
      'spotCoverage': instance.spotCoverage,
      'wilting': instance.wilting,
      'damage': instance.damage,
      'grade': _$PhysicalConditionGradeEnumMap[instance.grade]!,
    };

const _$PhysicalConditionGradeEnumMap = {
  PhysicalConditionGrade.excellent: 'excellent',
  PhysicalConditionGrade.good: 'good',
  PhysicalConditionGrade.fair: 'fair',
  PhysicalConditionGrade.poor: 'poor',
};

LeafRecommendation _$LeafRecommendationFromJson(Map<String, dynamic> json) =>
    LeafRecommendation(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
      urgency: $enumDecode(_$RecommendationUrgencyEnumMap, json['urgency']),
      actions:
          (json['actions'] as List<dynamic>).map((e) => e as String).toList(),
      parameters: json['parameters'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$LeafRecommendationToJson(LeafRecommendation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$RecommendationTypeEnumMap[instance.type]!,
      'urgency': _$RecommendationUrgencyEnumMap[instance.urgency]!,
      'actions': instance.actions,
      'parameters': instance.parameters,
    };

const _$RecommendationTypeEnumMap = {
  RecommendationType.treatment: 'treatment',
  RecommendationType.prevention: 'prevention',
  RecommendationType.monitoring: 'monitoring',
  RecommendationType.fertilization: 'fertilization',
};

const _$RecommendationUrgencyEnumMap = {
  RecommendationUrgency.immediate: 'immediate',
  RecommendationUrgency.urgent: 'urgent',
  RecommendationUrgency.moderate: 'moderate',
  RecommendationUrgency.low: 'low',
};
