from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List
import uuid
import os
from datetime import datetime

from ..database import get_db
from ..auth import get_current_active_user
from ..models import User, Farm, SoilTest
from ..schemas import SoilTestCreate, SoilTestUpdate, SoilTestResponse
from ..config import settings

router = APIRouter()

@router.post("/upload", response_model=SoilTestResponse)
async def upload_soil_card(
    farm_id: str,
    sample_location: str,
    file: UploadFile = File(...),
    notes: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload soil health card image for analysis"""
    # Verify farm ownership
    farm = db.query(Farm).filter(Farm.id == farm_id).first()
    if not farm:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Farm not found"
        )
    
    if farm.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Validate file
    if file.content_type not in ["image/jpeg", "image/png", "image/jpg"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only JPEG and PNG images are allowed"
        )
    
    try:
        # Save uploaded file
        file_extension = os.path.splitext(file.filename)[1]
        filename = f"soil_{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.UPLOAD_DIR, filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Create soil test record
        soil_test_id = str(uuid.uuid4())
        db_soil_test = SoilTest(
            id=soil_test_id,
            user_id=current_user.id,
            farm_id=farm_id,
            sample_location=sample_location,
            collection_date=datetime.utcnow(),
            status="processing",
            report_image_url=file_path,
            notes=notes
        )
        
        db.add(db_soil_test)
        db.commit()
        db.refresh(db_soil_test)
        
        # TODO: Trigger background task for OCR and analysis
        # For now, simulate analysis with mock data
        await simulate_soil_analysis(db_soil_test, db)
        
        return SoilTestResponse.from_orm(db_soil_test)
        
    except Exception as e:
        db.rollback()
        # Clean up uploaded file if database operation fails
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload soil card: {str(e)}"
        )

async def simulate_soil_analysis(soil_test: SoilTest, db: Session):
    """Simulate soil analysis with mock data"""
    try:
        # Mock analysis results
        soil_test.analysis_date = datetime.utcnow()
        soil_test.status = "completed"
        soil_test.ph_level = 6.5
        soil_test.nitrogen = 250.0
        soil_test.phosphorus = 15.0
        soil_test.potassium = 180.0
        soil_test.organic_matter = 2.8
        soil_test.overall_health = "good"
        soil_test.recommendations = {
            "fertilizers": [
                {
                    "name": "Nitrogen Fertilizer",
                    "quantity": "50 kg/ha",
                    "timing": "Before planting"
                }
            ],
            "general_advice": [
                "Apply nitrogen fertilizer",
                "Monitor soil moisture",
                "Consider organic matter addition"
            ]
        }
        
        db.commit()
        db.refresh(soil_test)
        
    except Exception as e:
        print(f"Error in soil analysis simulation: {e}")

@router.get("/", response_model=List[SoilTestResponse])
async def list_soil_tests(
    farm_id: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get soil tests for user's farms"""
    query = db.query(SoilTest).filter(SoilTest.user_id == current_user.id)
    
    if farm_id:
        # Verify farm ownership
        farm = db.query(Farm).filter(Farm.id == farm_id).first()
        if not farm or farm.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        query = query.filter(SoilTest.farm_id == farm_id)
    
    soil_tests = query.order_by(SoilTest.created_at.desc()).all()
    return [SoilTestResponse.from_orm(test) for test in soil_tests]

@router.get("/{test_id}", response_model=SoilTestResponse)
async def get_soil_test(
    test_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get soil test by ID"""
    soil_test = db.query(SoilTest).filter(SoilTest.id == test_id).first()
    
    if not soil_test:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil test not found"
        )
    
    # Check ownership
    if soil_test.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return SoilTestResponse.from_orm(soil_test)

@router.put("/{test_id}", response_model=SoilTestResponse)
async def update_soil_test(
    test_id: str,
    soil_update: SoilTestUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update soil test (for experts/admins)"""
    soil_test = db.query(SoilTest).filter(SoilTest.id == test_id).first()
    
    if not soil_test:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil test not found"
        )
    
    # Check permissions (owner or expert/admin)
    if (soil_test.user_id != current_user.id and 
        current_user.role not in ["expert", "admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Update soil test fields
        update_data = soil_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(soil_test, field):
                setattr(soil_test, field, value)
        
        db.commit()
        db.refresh(soil_test)
        
        return SoilTestResponse.from_orm(soil_test)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update soil test: {str(e)}"
        )

@router.delete("/{test_id}")
async def delete_soil_test(
    test_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete soil test"""
    soil_test = db.query(SoilTest).filter(SoilTest.id == test_id).first()
    
    if not soil_test:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Soil test not found"
        )
    
    # Check ownership
    if soil_test.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Delete associated file
        if soil_test.report_image_url and os.path.exists(soil_test.report_image_url):
            os.remove(soil_test.report_image_url)
        
        db.delete(soil_test)
        db.commit()
        
        return {"message": "Soil test deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete soil test: {str(e)}"
        )
