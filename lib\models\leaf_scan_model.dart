import 'package:json_annotation/json_annotation.dart';

part 'leaf_scan_model.g.dart';

@JsonSerializable()
class LeafScanModel {
  final String id;
  final String farmId;
  final String cropId;
  final String imageUrl;
  final DateTime scanDate;
  final LeafAnalysisResults analysisResults;
  final List<LeafRecommendation> recommendations;
  final ScanStatus status;
  final double? confidence;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LeafScanModel({
    required this.id,
    required this.farmId,
    required this.cropId,
    required this.imageUrl,
    required this.scanDate,
    required this.analysisResults,
    required this.recommendations,
    this.status = ScanStatus.processing,
    this.confidence,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LeafScanModel.fromJson(Map<String, dynamic> json) =>
      _$LeafScanModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeafScanModelToJson(this);

  bool get isCompleted => status == ScanStatus.completed;
  bool get hasDiseases => analysisResults.diseases.isNotEmpty;
  bool get hasDeficiencies => analysisResults.deficiencies.isNotEmpty;
  bool get hasIssues => hasDiseases || hasDeficiencies;

  LeafHealthGrade get overallHealth {
    if (!isCompleted) return LeafHealthGrade.unknown;
    
    if (!hasIssues) return LeafHealthGrade.healthy;
    
    final severityScores = [
      ...analysisResults.diseases.map((d) => d.severity.score),
      ...analysisResults.deficiencies.map((d) => d.severity.score),
    ];
    
    if (severityScores.isEmpty) return LeafHealthGrade.healthy;
    
    final maxSeverity = severityScores.reduce((a, b) => a > b ? a : b);
    
    if (maxSeverity >= 80) return LeafHealthGrade.critical;
    if (maxSeverity >= 60) return LeafHealthGrade.poor;
    if (maxSeverity >= 40) return LeafHealthGrade.fair;
    return LeafHealthGrade.good;
  }

  int get daysSinceScan => DateTime.now().difference(scanDate).inDays;

  LeafScanModel copyWith({
    String? id,
    String? farmId,
    String? cropId,
    String? imageUrl,
    DateTime? scanDate,
    LeafAnalysisResults? analysisResults,
    List<LeafRecommendation>? recommendations,
    ScanStatus? status,
    double? confidence,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeafScanModel(
      id: id ?? this.id,
      farmId: farmId ?? this.farmId,
      cropId: cropId ?? this.cropId,
      imageUrl: imageUrl ?? this.imageUrl,
      scanDate: scanDate ?? this.scanDate,
      analysisResults: analysisResults ?? this.analysisResults,
      recommendations: recommendations ?? this.recommendations,
      status: status ?? this.status,
      confidence: confidence ?? this.confidence,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class LeafAnalysisResults {
  final LeafColorAnalysis colorAnalysis;
  final List<DiseaseDetection> diseases;
  final List<NutrientDeficiency> deficiencies;
  final List<PestDetection> pests;
  final LeafPhysicalCondition physicalCondition;

  const LeafAnalysisResults({
    required this.colorAnalysis,
    required this.diseases,
    required this.deficiencies,
    required this.pests,
    required this.physicalCondition,
  });

  factory LeafAnalysisResults.fromJson(Map<String, dynamic> json) =>
      _$LeafAnalysisResultsFromJson(json);

  Map<String, dynamic> toJson() => _$LeafAnalysisResultsToJson(this);

  List<String> get allIssues {
    return [
      ...diseases.map((d) => d.name),
      ...deficiencies.map((d) => d.nutrient),
      ...pests.map((p) => p.name),
    ];
  }
}

@JsonSerializable()
class LeafColorAnalysis {
  final String dominantColor;
  final double greenness;
  final double yellowness;
  final double browning;
  final Map<String, double> colorDistribution;
  final ColorHealthIndicator healthIndicator;

  const LeafColorAnalysis({
    required this.dominantColor,
    required this.greenness,
    required this.yellowness,
    required this.browning,
    required this.colorDistribution,
    required this.healthIndicator,
  });

  factory LeafColorAnalysis.fromJson(Map<String, dynamic> json) =>
      _$LeafColorAnalysisFromJson(json);

  Map<String, dynamic> toJson() => _$LeafColorAnalysisToJson(this);
}

@JsonSerializable()
class DiseaseDetection {
  final String id;
  final String name;
  final String scientificName;
  final double confidence;
  final DiseaseSeverity severity;
  final String description;
  final List<String> symptoms;
  final List<String> causes;
  final List<String> treatments;

  const DiseaseDetection({
    required this.id,
    required this.name,
    required this.scientificName,
    required this.confidence,
    required this.severity,
    required this.description,
    required this.symptoms,
    required this.causes,
    required this.treatments,
  });

  factory DiseaseDetection.fromJson(Map<String, dynamic> json) =>
      _$DiseaseDetectionFromJson(json);

  Map<String, dynamic> toJson() => _$DiseaseDetectionToJson(this);
}

@JsonSerializable()
class NutrientDeficiency {
  final String nutrient;
  final String name;
  final double confidence;
  final DeficiencySeverity severity;
  final String description;
  final List<String> symptoms;
  final List<String> solutions;

  const NutrientDeficiency({
    required this.nutrient,
    required this.name,
    required this.confidence,
    required this.severity,
    required this.description,
    required this.symptoms,
    required this.solutions,
  });

  factory NutrientDeficiency.fromJson(Map<String, dynamic> json) =>
      _$NutrientDeficiencyFromJson(json);

  Map<String, dynamic> toJson() => _$NutrientDeficiencyToJson(this);
}

@JsonSerializable()
class PestDetection {
  final String id;
  final String name;
  final String scientificName;
  final double confidence;
  final PestSeverity severity;
  final String description;
  final List<String> symptoms;
  final List<String> treatments;

  const PestDetection({
    required this.id,
    required this.name,
    required this.scientificName,
    required this.confidence,
    required this.severity,
    required this.description,
    required this.symptoms,
    required this.treatments,
  });

  factory PestDetection.fromJson(Map<String, dynamic> json) =>
      _$PestDetectionFromJson(json);

  Map<String, dynamic> toJson() => _$PestDetectionToJson(this);
}

@JsonSerializable()
class LeafPhysicalCondition {
  final double leafArea;
  final double spotCoverage;
  final double wilting;
  final double damage;
  final PhysicalConditionGrade grade;

  const LeafPhysicalCondition({
    required this.leafArea,
    required this.spotCoverage,
    required this.wilting,
    required this.damage,
    required this.grade,
  });

  factory LeafPhysicalCondition.fromJson(Map<String, dynamic> json) =>
      _$LeafPhysicalConditionFromJson(json);

  Map<String, dynamic> toJson() => _$LeafPhysicalConditionToJson(this);
}

@JsonSerializable()
class LeafRecommendation {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final RecommendationUrgency urgency;
  final List<String> actions;
  final Map<String, dynamic>? parameters;

  const LeafRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.urgency,
    required this.actions,
    this.parameters,
  });

  factory LeafRecommendation.fromJson(Map<String, dynamic> json) =>
      _$LeafRecommendationFromJson(json);

  Map<String, dynamic> toJson() => _$LeafRecommendationToJson(this);
}

// Enums
enum ScanStatus {
  @JsonValue('processing')
  processing,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
}

enum LeafHealthGrade {
  @JsonValue('healthy')
  healthy,
  @JsonValue('good')
  good,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
  @JsonValue('critical')
  critical,
  @JsonValue('unknown')
  unknown,
}

enum ColorHealthIndicator {
  @JsonValue('healthy')
  healthy,
  @JsonValue('stressed')
  stressed,
  @JsonValue('deficient')
  deficient,
  @JsonValue('diseased')
  diseased,
}

enum DiseaseSeverity {
  @JsonValue('mild')
  mild,
  @JsonValue('moderate')
  moderate,
  @JsonValue('severe')
  severe,
  @JsonValue('critical')
  critical,
}

enum DeficiencySeverity {
  @JsonValue('mild')
  mild,
  @JsonValue('moderate')
  moderate,
  @JsonValue('severe')
  severe,
}

enum PestSeverity {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

enum PhysicalConditionGrade {
  @JsonValue('excellent')
  excellent,
  @JsonValue('good')
  good,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
}

enum RecommendationType {
  @JsonValue('treatment')
  treatment,
  @JsonValue('prevention')
  prevention,
  @JsonValue('monitoring')
  monitoring,
  @JsonValue('fertilization')
  fertilization,
}

enum RecommendationUrgency {
  @JsonValue('immediate')
  immediate,
  @JsonValue('urgent')
  urgent,
  @JsonValue('moderate')
  moderate,
  @JsonValue('low')
  low,
}

// Extensions
extension DiseaseSeverityExtension on DiseaseSeverity {
  double get score {
    switch (this) {
      case DiseaseSeverity.mild:
        return 25.0;
      case DiseaseSeverity.moderate:
        return 50.0;
      case DiseaseSeverity.severe:
        return 75.0;
      case DiseaseSeverity.critical:
        return 100.0;
    }
  }
}

extension DeficiencySeverityExtension on DeficiencySeverity {
  double get score {
    switch (this) {
      case DeficiencySeverity.mild:
        return 30.0;
      case DeficiencySeverity.moderate:
        return 60.0;
      case DeficiencySeverity.severe:
        return 90.0;
    }
  }
}
