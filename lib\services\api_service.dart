import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/user_model.dart';
import '../models/farm_model.dart';
import '../models/soil_model.dart';
import '../models/leaf_scan_model.dart';
import '../models/recommendation_model.dart';
import '../utils/constants.dart';

// Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

// Handles HTTP requests to backend
class ApiService {
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token to requests
        final token = await _getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          // Token expired, try to refresh
          final newToken = await _refreshAuthToken();
          if (newToken != null) {
            // Retry the request with new token
            final options = error.requestOptions;
            options.headers['Authorization'] = 'Bearer $newToken';
            try {
              final response = await _dio.fetch(options);
              handler.resolve(response);
              return;
            } catch (e) {
              // If retry fails, continue with original error
            }
          }
        }
        handler.next(error);
      },
    ));
  }

  // User Management
  Future<UserModel> getUserProfile(String uid) async {
    try {
      final response = await _dio.get('/users/$uid');
      return UserModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<UserModel> createUserProfile({
    required String uid,
    required String email,
    required String firstName,
    required String lastName,
    String? phoneNumber,
  }) async {
    try {
      final response = await _dio.post('/users', data: {
        'uid': uid,
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
        'phoneNumber': phoneNumber,
      });
      return UserModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<UserModel> updateUserProfile({
    required String uid,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (firstName != null) data['firstName'] = firstName;
      if (lastName != null) data['lastName'] = lastName;
      if (phoneNumber != null) data['phoneNumber'] = phoneNumber;
      if (profileImageUrl != null) data['profileImageUrl'] = profileImageUrl;

      final response = await _dio.put('/users/$uid', data: data);
      return UserModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> deleteUserProfile(String uid) async {
    try {
      await _dio.delete('/users/$uid');
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Farm Management
  Future<List<FarmModel>> getUserFarms(String userId) async {
    try {
      final response = await _dio.get('/farms', queryParameters: {
        'userId': userId,
      });
      return (response.data as List)
          .map((farm) => FarmModel.fromJson(farm))
          .toList();
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<FarmModel> getFarm(String farmId) async {
    try {
      final response = await _dio.get('/farms/$farmId');
      return FarmModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<FarmModel> createFarm(Map<String, dynamic> farmData) async {
    try {
      final response = await _dio.post('/farms', data: farmData);
      return FarmModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<FarmModel> updateFarm(
      String farmId, Map<String, dynamic> farmData) async {
    try {
      final response = await _dio.put('/farms/$farmId', data: farmData);
      return FarmModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> deleteFarm(String farmId) async {
    try {
      await _dio.delete('/farms/$farmId');
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Authentication methods
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'username': email, // FastAPI OAuth2PasswordRequestForm uses 'username'
        'password': password,
      });

      // Store token
      final token = response.data['access_token'];
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.authTokenKey, token);

      return response.data;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
  }) async {
    try {
      final response = await _dio.post('/auth/register', data: {
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'phone_number': phoneNumber,
        'role': 'farmer',
      });

      // Store token
      final token = response.data['access_token'];
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.authTokenKey, token);

      return response.data;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.authTokenKey);
    await prefs.remove(AppConstants.userDataKey);
  }

  // Soil Analysis
  Future<Map<String, dynamic>> uploadSoilCard({
    required String farmId,
    required String sampleLocation,
    required File imageFile,
    String? notes,
  }) async {
    try {
      final formData = FormData.fromMap({
        'farm_id': farmId,
        'sample_location': sampleLocation,
        'file': await MultipartFile.fromFile(imageFile.path),
        if (notes != null) 'notes': notes,
      });

      final response = await _dio.post('/soil/upload', data: formData);
      return response.data;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<List<Map<String, dynamic>>> getSoilTests({String? farmId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (farmId != null) queryParams['farm_id'] = farmId;

      final response = await _dio.get('/soil/', queryParameters: queryParams);
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Leaf Scanning
  Future<Map<String, dynamic>> uploadLeafImage({
    required String farmId,
    required File imageFile,
    String? cropId,
  }) async {
    try {
      final formData = FormData.fromMap({
        'farm_id': farmId,
        'file': await MultipartFile.fromFile(imageFile.path),
        if (cropId != null) 'crop_id': cropId,
      });

      final response = await _dio.post('/leaf-scan/upload', data: formData);
      return response.data;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<List<Map<String, dynamic>>> getLeafScans({String? farmId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (farmId != null) queryParams['farm_id'] = farmId;

      final response =
          await _dio.get('/leaf-scan/', queryParameters: queryParams);
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Recommendations
  Future<List<Map<String, dynamic>>> getRecommendations({
    String? farmId,
    String? status,
    String? priority,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (farmId != null) queryParams['farm_id'] = farmId;
      if (status != null) queryParams['status_filter'] = status;
      if (priority != null) queryParams['priority'] = priority;

      final response =
          await _dio.get('/recommendations/', queryParameters: queryParams);
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Helper methods
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.authTokenKey);
  }

  Future<String?> _refreshAuthToken() async {
    try {
      final response = await _dio.post('/auth/refresh');
      final token = response.data['access_token'];

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.authTokenKey, token);

      return token;
    } catch (e) {
      return null;
    }
  }
}

// API Exception class
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? code;

  ApiException({
    required this.message,
    this.statusCode,
    this.code,
  });

  factory ApiException.fromDioError(DioException error) {
    String message = 'An unexpected error occurred';
    int? statusCode = error.response?.statusCode;
    String? code;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Send timeout';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Receive timeout';
        break;
      case DioExceptionType.badResponse:
        final data = error.response?.data;
        if (data is Map<String, dynamic>) {
          message = data['message'] ?? message;
          code = data['code'];
        }
        break;
      case DioExceptionType.cancel:
        message = 'Request cancelled';
        break;
      case DioExceptionType.connectionError:
        message = 'Connection error';
        break;
      case DioExceptionType.unknown:
        message = 'Unknown error occurred';
        break;
      default:
        message = error.message ?? message;
    }

    return ApiException(
      message: message,
      statusCode: statusCode,
      code: code,
    );
  }

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode, Code: $code)';
  }
}
