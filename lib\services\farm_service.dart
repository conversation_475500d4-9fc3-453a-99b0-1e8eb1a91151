import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../database/database_helper.dart';
import '../models/sqlite_farm_model.dart';

class FarmService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Generate unique ID
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return '${timestamp}_$random';
  }

  // Create a new farm
  Future<SqliteFarm> createFarm({
    required String userId,
    required String name,
    String? description,
    required double latitude,
    required double longitude,
    required String address,
    required String city,
    required String state,
    required String country,
    String? postalCode,
    required double area,
    String areaUnit = 'hectares',
    required String farmType,
  }) async {
    final farm = SqliteFarm(
      id: _generateId(),
      userId: userId,
      name: name,
      description: description,
      latitude: latitude,
      longitude: longitude,
      address: address,
      city: city,
      state: state,
      country: country,
      postalCode: postalCode,
      area: area,
      areaUnit: areaUnit,
      farmType: farmType,
      createdAt: DateTime.now(),
    );

    await _dbHelper.insert('farms', farm.toMap());
    return farm;
  }

  // Get farms for a user
  Future<List<SqliteFarm>> getFarmsForUser(String userId) async {
    final farmMaps = await _dbHelper.query(
      'farms',
      where: 'user_id = ? AND is_active = 1',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return farmMaps.map((map) => SqliteFarm.fromMap(map)).toList();
  }

  // Get farm by ID
  Future<SqliteFarm?> getFarmById(String farmId) async {
    final farmMaps = await _dbHelper.query(
      'farms',
      where: 'id = ? AND is_active = 1',
      whereArgs: [farmId],
    );

    if (farmMaps.isNotEmpty) {
      return SqliteFarm.fromMap(farmMaps.first);
    }
    return null;
  }

  // Update farm
  Future<SqliteFarm?> updateFarm({
    required String farmId,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    double? area,
    String? areaUnit,
    String? farmType,
  }) async {
    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (name != null) updateData['name'] = name;
    if (description != null) updateData['description'] = description;
    if (latitude != null) updateData['latitude'] = latitude;
    if (longitude != null) updateData['longitude'] = longitude;
    if (address != null) updateData['address'] = address;
    if (city != null) updateData['city'] = city;
    if (state != null) updateData['state'] = state;
    if (country != null) updateData['country'] = country;
    if (postalCode != null) updateData['postal_code'] = postalCode;
    if (area != null) updateData['area'] = area;
    if (areaUnit != null) updateData['area_unit'] = areaUnit;
    if (farmType != null) updateData['farm_type'] = farmType;

    final rowsAffected = await _dbHelper.update(
      'farms',
      updateData,
      where: 'id = ?',
      whereArgs: [farmId],
    );

    if (rowsAffected > 0) {
      return await getFarmById(farmId);
    }
    return null;
  }

  // Delete farm (soft delete)
  Future<bool> deleteFarm(String farmId) async {
    final rowsAffected = await _dbHelper.update(
      'farms',
      {
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [farmId],
    );

    return rowsAffected > 0;
  }

  // Create a new crop
  Future<SqliteCrop> createCrop({
    required String farmId,
    required String name,
    required String variety,
    required String cropType,
    required DateTime plantingDate,
    DateTime? harvestDate,
    required String stage,
    required double areaAllocated,
    String healthStatus = 'good',
  }) async {
    final crop = SqliteCrop(
      id: _generateId(),
      farmId: farmId,
      name: name,
      variety: variety,
      cropType: cropType,
      plantingDate: plantingDate,
      harvestDate: harvestDate,
      stage: stage,
      areaAllocated: areaAllocated,
      healthStatus: healthStatus,
      createdAt: DateTime.now(),
    );

    await _dbHelper.insert('crops', crop.toMap());
    return crop;
  }

  // Get crops for a farm
  Future<List<SqliteCrop>> getCropsForFarm(String farmId) async {
    final cropMaps = await _dbHelper.query(
      'crops',
      where: 'farm_id = ?',
      whereArgs: [farmId],
      orderBy: 'created_at DESC',
    );

    return cropMaps.map((map) => SqliteCrop.fromMap(map)).toList();
  }

  // Get crop by ID
  Future<SqliteCrop?> getCropById(String cropId) async {
    final cropMaps = await _dbHelper.query(
      'crops',
      where: 'id = ?',
      whereArgs: [cropId],
    );

    if (cropMaps.isNotEmpty) {
      return SqliteCrop.fromMap(cropMaps.first);
    }
    return null;
  }

  // Update crop
  Future<SqliteCrop?> updateCrop({
    required String cropId,
    String? name,
    String? variety,
    String? cropType,
    DateTime? plantingDate,
    DateTime? harvestDate,
    String? stage,
    double? areaAllocated,
    String? healthStatus,
  }) async {
    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (name != null) updateData['name'] = name;
    if (variety != null) updateData['variety'] = variety;
    if (cropType != null) updateData['crop_type'] = cropType;
    if (plantingDate != null) updateData['planting_date'] = plantingDate.toIso8601String();
    if (harvestDate != null) updateData['harvest_date'] = harvestDate.toIso8601String();
    if (stage != null) updateData['stage'] = stage;
    if (areaAllocated != null) updateData['area_allocated'] = areaAllocated;
    if (healthStatus != null) updateData['health_status'] = healthStatus;

    final rowsAffected = await _dbHelper.update(
      'crops',
      updateData,
      where: 'id = ?',
      whereArgs: [cropId],
    );

    if (rowsAffected > 0) {
      return await getCropById(cropId);
    }
    return null;
  }

  // Delete crop
  Future<bool> deleteCrop(String cropId) async {
    final rowsAffected = await _dbHelper.delete(
      'crops',
      where: 'id = ?',
      whereArgs: [cropId],
    );

    return rowsAffected > 0;
  }

  // Get farm statistics
  Future<Map<String, dynamic>> getFarmStatistics(String farmId) async {
    final crops = await getCropsForFarm(farmId);
    
    final totalCrops = crops.length;
    final totalArea = crops.fold<double>(0, (sum, crop) => sum + crop.areaAllocated);
    
    final healthyCrops = crops.where((crop) => crop.healthStatus == 'good').length;
    final unhealthyCrops = crops.where((crop) => crop.healthStatus == 'poor').length;
    
    final cropsByStage = <String, int>{};
    for (final crop in crops) {
      cropsByStage[crop.stage] = (cropsByStage[crop.stage] ?? 0) + 1;
    }

    return {
      'totalCrops': totalCrops,
      'totalArea': totalArea,
      'healthyCrops': healthyCrops,
      'unhealthyCrops': unhealthyCrops,
      'cropsByStage': cropsByStage,
    };
  }
}

// Provider for the farm service
final farmServiceProvider = Provider<FarmService>((ref) {
  return FarmService();
});

// Provider for user farms
final userFarmsProvider = FutureProvider.family<List<SqliteFarm>, String>((ref, userId) async {
  final farmService = ref.read(farmServiceProvider);
  return await farmService.getFarmsForUser(userId);
});

// Provider for farm crops
final farmCropsProvider = FutureProvider.family<List<SqliteCrop>, String>((ref, farmId) async {
  final farmService = ref.read(farmServiceProvider);
  return await farmService.getCropsForFarm(farmId);
});

// Provider for farm statistics
final farmStatisticsProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, farmId) async {
  final farmService = ref.read(farmServiceProvider);
  return await farmService.getFarmStatistics(farmId);
});
