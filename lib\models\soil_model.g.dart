// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'soil_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SoilModel _$SoilModelFromJson(Map<String, dynamic> json) => SoilModel(
      id: json['id'] as String,
      farmId: json['farmId'] as String,
      sampleLocation: json['sampleLocation'] as String,
      collectionDate: DateTime.parse(json['collectionDate'] as String),
      analysisDate: json['analysisDate'] == null
          ? null
          : DateTime.parse(json['analysisDate'] as String),
      testResults:
          SoilTestResults.fromJson(json['testResults'] as Map<String, dynamic>),
      recommendations: SoilRecommendations.fromJson(
          json['recommendations'] as Map<String, dynamic>),
      reportImageUrl: json['reportImageUrl'] as String?,
      labName: json['labName'] as String?,
      status: json['status'] as String? ?? 'pending',
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SoilModelToJson(SoilModel instance) => <String, dynamic>{
      'id': instance.id,
      'farmId': instance.farmId,
      'sampleLocation': instance.sampleLocation,
      'collectionDate': instance.collectionDate.toIso8601String(),
      'analysisDate': instance.analysisDate?.toIso8601String(),
      'testResults': instance.testResults,
      'recommendations': instance.recommendations,
      'reportImageUrl': instance.reportImageUrl,
      'labName': instance.labName,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

SoilTestResults _$SoilTestResultsFromJson(Map<String, dynamic> json) =>
    SoilTestResults(
      phLevel: SoilParameter.fromJson(json['phLevel'] as Map<String, dynamic>),
      nitrogen:
          SoilParameter.fromJson(json['nitrogen'] as Map<String, dynamic>),
      phosphorus:
          SoilParameter.fromJson(json['phosphorus'] as Map<String, dynamic>),
      potassium:
          SoilParameter.fromJson(json['potassium'] as Map<String, dynamic>),
      organicMatter:
          SoilParameter.fromJson(json['organicMatter'] as Map<String, dynamic>),
      calcium: json['calcium'] == null
          ? null
          : SoilParameter.fromJson(json['calcium'] as Map<String, dynamic>),
      magnesium: json['magnesium'] == null
          ? null
          : SoilParameter.fromJson(json['magnesium'] as Map<String, dynamic>),
      sulfur: json['sulfur'] == null
          ? null
          : SoilParameter.fromJson(json['sulfur'] as Map<String, dynamic>),
      zinc: json['zinc'] == null
          ? null
          : SoilParameter.fromJson(json['zinc'] as Map<String, dynamic>),
      iron: json['iron'] == null
          ? null
          : SoilParameter.fromJson(json['iron'] as Map<String, dynamic>),
      manganese: json['manganese'] == null
          ? null
          : SoilParameter.fromJson(json['manganese'] as Map<String, dynamic>),
      copper: json['copper'] == null
          ? null
          : SoilParameter.fromJson(json['copper'] as Map<String, dynamic>),
      boron: json['boron'] == null
          ? null
          : SoilParameter.fromJson(json['boron'] as Map<String, dynamic>),
      electricalConductivity:
          (json['electricalConductivity'] as num?)?.toDouble(),
      soilTexture: json['soilTexture'] as String?,
    );

Map<String, dynamic> _$SoilTestResultsToJson(SoilTestResults instance) =>
    <String, dynamic>{
      'phLevel': instance.phLevel,
      'nitrogen': instance.nitrogen,
      'phosphorus': instance.phosphorus,
      'potassium': instance.potassium,
      'organicMatter': instance.organicMatter,
      'calcium': instance.calcium,
      'magnesium': instance.magnesium,
      'sulfur': instance.sulfur,
      'zinc': instance.zinc,
      'iron': instance.iron,
      'manganese': instance.manganese,
      'copper': instance.copper,
      'boron': instance.boron,
      'electricalConductivity': instance.electricalConductivity,
      'soilTexture': instance.soilTexture,
    };

SoilParameter _$SoilParameterFromJson(Map<String, dynamic> json) =>
    SoilParameter(
      name: json['name'] as String,
      value: (json['value'] as num).toDouble(),
      unit: json['unit'] as String,
      optimalMin: (json['optimalMin'] as num).toDouble(),
      optimalMax: (json['optimalMax'] as num).toDouble(),
      status: $enumDecode(_$SoilParameterStatusEnumMap, json['status']),
      interpretation: json['interpretation'] as String?,
    );

Map<String, dynamic> _$SoilParameterToJson(SoilParameter instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
      'unit': instance.unit,
      'optimalMin': instance.optimalMin,
      'optimalMax': instance.optimalMax,
      'status': _$SoilParameterStatusEnumMap[instance.status]!,
      'interpretation': instance.interpretation,
    };

const _$SoilParameterStatusEnumMap = {
  SoilParameterStatus.deficient: 'deficient',
  SoilParameterStatus.optimal: 'optimal',
  SoilParameterStatus.excessive: 'excessive',
};

SoilRecommendations _$SoilRecommendationsFromJson(Map<String, dynamic> json) =>
    SoilRecommendations(
      fertilizers: (json['fertilizers'] as List<dynamic>)
          .map((e) =>
              FertilizerRecommendation.fromJson(e as Map<String, dynamic>))
          .toList(),
      amendments: (json['amendments'] as List<dynamic>)
          .map((e) =>
              AmendmentRecommendation.fromJson(e as Map<String, dynamic>))
          .toList(),
      generalAdvice: (json['generalAdvice'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      cropSuitability: CropSuitability.fromJson(
          json['cropSuitability'] as Map<String, dynamic>),
      nextTestingDate: DateTime.parse(json['nextTestingDate'] as String),
    );

Map<String, dynamic> _$SoilRecommendationsToJson(
        SoilRecommendations instance) =>
    <String, dynamic>{
      'fertilizers': instance.fertilizers,
      'amendments': instance.amendments,
      'generalAdvice': instance.generalAdvice,
      'cropSuitability': instance.cropSuitability,
      'nextTestingDate': instance.nextTestingDate.toIso8601String(),
    };

FertilizerRecommendation _$FertilizerRecommendationFromJson(
        Map<String, dynamic> json) =>
    FertilizerRecommendation(
      name: json['name'] as String,
      type: json['type'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unit: json['unit'] as String,
      applicationMethod: json['applicationMethod'] as String,
      timing: json['timing'] as String,
      purpose: json['purpose'] as String,
      costEstimate: (json['costEstimate'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$FertilizerRecommendationToJson(
        FertilizerRecommendation instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'quantity': instance.quantity,
      'unit': instance.unit,
      'applicationMethod': instance.applicationMethod,
      'timing': instance.timing,
      'purpose': instance.purpose,
      'costEstimate': instance.costEstimate,
    };

AmendmentRecommendation _$AmendmentRecommendationFromJson(
        Map<String, dynamic> json) =>
    AmendmentRecommendation(
      name: json['name'] as String,
      purpose: json['purpose'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unit: json['unit'] as String,
      applicationMethod: json['applicationMethod'] as String,
      timing: json['timing'] as String,
      costEstimate: (json['costEstimate'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$AmendmentRecommendationToJson(
        AmendmentRecommendation instance) =>
    <String, dynamic>{
      'name': instance.name,
      'purpose': instance.purpose,
      'quantity': instance.quantity,
      'unit': instance.unit,
      'applicationMethod': instance.applicationMethod,
      'timing': instance.timing,
      'costEstimate': instance.costEstimate,
    };

CropSuitability _$CropSuitabilityFromJson(Map<String, dynamic> json) =>
    CropSuitability(
      suitableCrops: (json['suitableCrops'] as List<dynamic>)
          .map((e) => SuitableCrop.fromJson(e as Map<String, dynamic>))
          .toList(),
      moderatelySuitableCrops:
          (json['moderatelySuitableCrops'] as List<dynamic>)
              .map((e) => SuitableCrop.fromJson(e as Map<String, dynamic>))
              .toList(),
      unsuitableCrops: (json['unsuitableCrops'] as List<dynamic>)
          .map((e) => SuitableCrop.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CropSuitabilityToJson(CropSuitability instance) =>
    <String, dynamic>{
      'suitableCrops': instance.suitableCrops,
      'moderatelySuitableCrops': instance.moderatelySuitableCrops,
      'unsuitableCrops': instance.unsuitableCrops,
    };

SuitableCrop _$SuitableCropFromJson(Map<String, dynamic> json) => SuitableCrop(
      name: json['name'] as String,
      variety: json['variety'] as String,
      suitabilityScore: (json['suitabilityScore'] as num).toDouble(),
      reason: json['reason'] as String,
      requirements: (json['requirements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$SuitableCropToJson(SuitableCrop instance) =>
    <String, dynamic>{
      'name': instance.name,
      'variety': instance.variety,
      'suitabilityScore': instance.suitabilityScore,
      'reason': instance.reason,
      'requirements': instance.requirements,
    };
