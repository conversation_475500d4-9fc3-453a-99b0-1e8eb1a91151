# Android Command Line Tools Setup Script
Write-Host "Setting up Android Command Line Tools..." -ForegroundColor Green

# Define paths
$androidSdkPath = "$env:LOCALAPPDATA\Android\sdk"
$cmdlineToolsPath = "$androidSdkPath\cmdline-tools"
$latestPath = "$cmdlineToolsPath\latest"

# Create directories
Write-Host "Creating directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path $cmdlineToolsPath -Force | Out-Null
New-Item -ItemType Directory -Path $latestPath -Force | Out-Null

# Download URL for command line tools
$downloadUrl = "https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip"
$zipFile = "$env:TEMP\commandlinetools-win-latest.zip"

Write-Host "Downloading Android Command Line Tools..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Extract the zip file
Write-Host "Extracting command line tools..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath $env:TEMP -Force
    
    # Move contents to the correct location
    $extractedPath = "$env:TEMP\cmdline-tools"
    if (Test-Path $extractedPath) {
        Get-ChildItem -Path $extractedPath | Move-Item -Destination $latestPath -Force
        Write-Host "Command line tools installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Extraction failed - cmdline-tools folder not found" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Clean up
Remove-Item -Path $zipFile -Force -ErrorAction SilentlyContinue
Remove-Item -Path "$env:TEMP\cmdline-tools" -Recurse -Force -ErrorAction SilentlyContinue

# Set environment variables
Write-Host "Setting up environment variables..." -ForegroundColor Yellow
$env:ANDROID_HOME = $androidSdkPath
$env:PATH = "$env:PATH;$latestPath\bin;$androidSdkPath\platform-tools"

Write-Host "Android Command Line Tools setup completed!" -ForegroundColor Green
Write-Host "ANDROID_HOME: $androidSdkPath" -ForegroundColor Cyan
Write-Host "You may need to restart your terminal for changes to take effect." -ForegroundColor Yellow
