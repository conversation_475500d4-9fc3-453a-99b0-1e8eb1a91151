#!/usr/bin/env python3
"""
Simple test script to verify the backend is working
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ Root endpoint: {response.status_code}")
        print(f"   Response: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")
        return False

def test_api_health():
    """Test the API health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        print(f"✅ Health endpoint: {response.status_code}")
        print(f"   Response: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health endpoint failed: {e}")
        return False

def test_register_user():
    """Test user registration"""
    try:
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
            "phone_number": "+**********",
            "role": "farmer"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/register", json=user_data)
        print(f"✅ User registration: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   User created: {data['user']['email']}")
            print(f"   Token received: {data['access_token'][:20]}...")
            return data['access_token']
        else:
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ User registration failed: {e}")
        return None

def test_login_user():
    """Test user login"""
    try:
        login_data = {
            "username": "<EMAIL>",  # FastAPI OAuth2 uses 'username'
            "password": "testpassword123"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", data=login_data)
        print(f"✅ User login: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   User logged in: {data['user']['email']}")
            print(f"   Token received: {data['access_token'][:20]}...")
            return data['access_token']
        else:
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ User login failed: {e}")
        return None

def test_protected_endpoint(token):
    """Test a protected endpoint"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/users/me", headers=headers)
        print(f"✅ Protected endpoint: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   User profile: {data['first_name']} {data['last_name']}")
            return True
        else:
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Farm Management Python Backend")
    print("=" * 50)
    
    # Test basic connectivity
    if not test_health_check():
        print("\n❌ Backend is not running. Please start the server first.")
        print("Run: python run.py")
        sys.exit(1)
    
    # Test API health
    test_api_health()
    
    print("\n🔐 Testing Authentication...")
    
    # Test registration (might fail if user already exists)
    token = test_register_user()
    
    # Test login
    if not token:
        token = test_login_user()
    
    if token:
        print("\n🛡️ Testing Protected Endpoints...")
        test_protected_endpoint(token)
    
    print("\n✅ Backend testing completed!")
    print("🌐 API Documentation available at: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
