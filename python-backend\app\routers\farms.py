from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import uuid

from ..database import get_db
from ..auth import get_current_active_user
from ..models import User, Farm
from ..schemas import FarmCreate, FarmUpdate, FarmResponse

router = APIRouter()

@router.post("/", response_model=FarmResponse)
async def create_farm(
    farm: FarmCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new farm"""
    try:
        farm_id = str(uuid.uuid4())
        
        db_farm = Farm(
            id=farm_id,
            name=farm.name,
            description=farm.description,
            owner_id=current_user.id,
            latitude=farm.location.latitude,
            longitude=farm.location.longitude,
            address=farm.location.address,
            city=farm.location.city,
            state=farm.location.state,
            country=farm.location.country,
            postal_code=farm.location.postal_code,
            area=farm.area,
            area_unit=farm.area_unit,
            farm_type=farm.farm_type.value
        )
        
        db.add(db_farm)
        db.commit()
        db.refresh(db_farm)
        
        return FarmResponse.from_orm(db_farm)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create farm: {str(e)}"
        )

@router.get("/", response_model=List[FarmResponse])
async def list_user_farms(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all farms owned by current user"""
    farms = db.query(Farm).filter(
        Farm.owner_id == current_user.id,
        Farm.is_active == True
    ).all()
    
    return [FarmResponse.from_orm(farm) for farm in farms]

@router.get("/{farm_id}", response_model=FarmResponse)
async def get_farm(
    farm_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get farm by ID"""
    farm = db.query(Farm).filter(Farm.id == farm_id).first()
    
    if not farm:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Farm not found"
        )
    
    # Check if user owns the farm or is admin
    if farm.owner_id != current_user.id and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return FarmResponse.from_orm(farm)

@router.put("/{farm_id}", response_model=FarmResponse)
async def update_farm(
    farm_id: str,
    farm_update: FarmUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update farm"""
    farm = db.query(Farm).filter(Farm.id == farm_id).first()
    
    if not farm:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Farm not found"
        )
    
    # Check if user owns the farm
    if farm.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Update farm fields
        update_data = farm_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(farm, field):
                setattr(farm, field, value)
        
        db.commit()
        db.refresh(farm)
        
        return FarmResponse.from_orm(farm)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update farm: {str(e)}"
        )

@router.delete("/{farm_id}")
async def delete_farm(
    farm_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete farm (soft delete)"""
    farm = db.query(Farm).filter(Farm.id == farm_id).first()
    
    if not farm:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Farm not found"
        )
    
    # Check if user owns the farm
    if farm.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Soft delete
        farm.is_active = False
        db.commit()
        
        return {"message": "Farm deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete farm: {str(e)}"
        )
