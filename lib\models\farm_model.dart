import 'package:json_annotation/json_annotation.dart';

part 'farm_model.g.dart';

// Farm data model
@JsonSerializable()
class FarmModel {
  final String id;
  final String name;
  final String description;
  final String ownerId;
  final FarmLocation location;
  final double area; // in hectares
  final String areaUnit;
  final FarmType farmType;
  final List<CropInfo> crops;
  final SoilInfo? soilInfo;
  final WeatherInfo? weatherInfo;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final List<String> imageUrls;

  const FarmModel({
    required this.id,
    required this.name,
    required this.description,
    required this.ownerId,
    required this.location,
    required this.area,
    this.areaUnit = 'hectares',
    required this.farmType,
    this.crops = const [],
    this.soilInfo,
    this.weatherInfo,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.imageUrls = const [],
  });

  factory FarmModel.fromJson(Map<String, dynamic> json) =>
      _$FarmModelFromJson(json);

  Map<String, dynamic> toJson() => _$FarmModelToJson(this);

  FarmModel copyWith({
    String? id,
    String? name,
    String? description,
    String? ownerId,
    FarmLocation? location,
    double? area,
    String? areaUnit,
    FarmType? farmType,
    List<CropInfo>? crops,
    SoilInfo? soilInfo,
    WeatherInfo? weatherInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    List<String>? imageUrls,
  }) {
    return FarmModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      location: location ?? this.location,
      area: area ?? this.area,
      areaUnit: areaUnit ?? this.areaUnit,
      farmType: farmType ?? this.farmType,
      crops: crops ?? this.crops,
      soilInfo: soilInfo ?? this.soilInfo,
      weatherInfo: weatherInfo ?? this.weatherInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      imageUrls: imageUrls ?? this.imageUrls,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FarmModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FarmModel(id: $id, name: $name, area: $area $areaUnit)';
  }
}

@JsonSerializable()
class FarmLocation {
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String country;
  final String? postalCode;

  const FarmLocation({
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    this.postalCode,
  });

  factory FarmLocation.fromJson(Map<String, dynamic> json) =>
      _$FarmLocationFromJson(json);

  Map<String, dynamic> toJson() => _$FarmLocationToJson(this);

  String get fullAddress {
    final parts = [address, city, state, country];
    if (postalCode != null) parts.add(postalCode!);
    return parts.where((part) => part.isNotEmpty).join(', ');
  }

  FarmLocation copyWith({
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
  }) {
    return FarmLocation(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
    );
  }
}

@JsonSerializable()
class CropInfo {
  final String id;
  final String name;
  final String variety;
  final CropType type;
  final DateTime plantingDate;
  final DateTime? harvestDate;
  final CropStage stage;
  final double areaAllocated; // in hectares
  final List<String> imageUrls;
  final CropHealth health;

  const CropInfo({
    required this.id,
    required this.name,
    required this.variety,
    required this.type,
    required this.plantingDate,
    this.harvestDate,
    required this.stage,
    required this.areaAllocated,
    this.imageUrls = const [],
    required this.health,
  });

  factory CropInfo.fromJson(Map<String, dynamic> json) =>
      _$CropInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CropInfoToJson(this);

  int get daysFromPlanting {
    return DateTime.now().difference(plantingDate).inDays;
  }

  int? get daysToHarvest {
    if (harvestDate == null) return null;
    final diff = harvestDate!.difference(DateTime.now()).inDays;
    return diff > 0 ? diff : 0;
  }

  CropInfo copyWith({
    String? id,
    String? name,
    String? variety,
    CropType? type,
    DateTime? plantingDate,
    DateTime? harvestDate,
    CropStage? stage,
    double? areaAllocated,
    List<String>? imageUrls,
    CropHealth? health,
  }) {
    return CropInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      variety: variety ?? this.variety,
      type: type ?? this.type,
      plantingDate: plantingDate ?? this.plantingDate,
      harvestDate: harvestDate ?? this.harvestDate,
      stage: stage ?? this.stage,
      areaAllocated: areaAllocated ?? this.areaAllocated,
      imageUrls: imageUrls ?? this.imageUrls,
      health: health ?? this.health,
    );
  }
}

@JsonSerializable()
class SoilInfo {
  final String id;
  final double ph;
  final double nitrogen;
  final double phosphorus;
  final double potassium;
  final double organicMatter;
  final SoilType soilType;
  final SoilHealth health;
  final DateTime lastTested;
  final String? reportUrl;

  const SoilInfo({
    required this.id,
    required this.ph,
    required this.nitrogen,
    required this.phosphorus,
    required this.potassium,
    required this.organicMatter,
    required this.soilType,
    required this.health,
    required this.lastTested,
    this.reportUrl,
  });

  factory SoilInfo.fromJson(Map<String, dynamic> json) =>
      _$SoilInfoFromJson(json);

  Map<String, dynamic> toJson() => _$SoilInfoToJson(this);

  bool get isRecentlyTested {
    return DateTime.now().difference(lastTested).inDays <= 365;
  }

  SoilInfo copyWith({
    String? id,
    double? ph,
    double? nitrogen,
    double? phosphorus,
    double? potassium,
    double? organicMatter,
    SoilType? soilType,
    SoilHealth? health,
    DateTime? lastTested,
    String? reportUrl,
  }) {
    return SoilInfo(
      id: id ?? this.id,
      ph: ph ?? this.ph,
      nitrogen: nitrogen ?? this.nitrogen,
      phosphorus: phosphorus ?? this.phosphorus,
      potassium: potassium ?? this.potassium,
      organicMatter: organicMatter ?? this.organicMatter,
      soilType: soilType ?? this.soilType,
      health: health ?? this.health,
      lastTested: lastTested ?? this.lastTested,
      reportUrl: reportUrl ?? this.reportUrl,
    );
  }
}

@JsonSerializable()
class WeatherInfo {
  final double temperature;
  final double humidity;
  final double rainfall;
  final double windSpeed;
  final String condition;
  final DateTime timestamp;

  const WeatherInfo({
    required this.temperature,
    required this.humidity,
    required this.rainfall,
    required this.windSpeed,
    required this.condition,
    required this.timestamp,
  });

  factory WeatherInfo.fromJson(Map<String, dynamic> json) =>
      _$WeatherInfoFromJson(json);

  Map<String, dynamic> toJson() => _$WeatherInfoToJson(this);
}

// Enums
enum FarmType {
  @JsonValue('organic')
  organic,
  @JsonValue('conventional')
  conventional,
  @JsonValue('mixed')
  mixed,
}

enum CropType {
  @JsonValue('cereal')
  cereal,
  @JsonValue('vegetable')
  vegetable,
  @JsonValue('fruit')
  fruit,
  @JsonValue('legume')
  legume,
  @JsonValue('cash_crop')
  cashCrop,
}

enum CropStage {
  @JsonValue('seedling')
  seedling,
  @JsonValue('vegetative')
  vegetative,
  @JsonValue('flowering')
  flowering,
  @JsonValue('fruiting')
  fruiting,
  @JsonValue('maturity')
  maturity,
  @JsonValue('harvested')
  harvested,
}

enum CropHealth {
  @JsonValue('excellent')
  excellent,
  @JsonValue('good')
  good,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
  @JsonValue('critical')
  critical,
}

enum SoilType {
  @JsonValue('clay')
  clay,
  @JsonValue('sandy')
  sandy,
  @JsonValue('loam')
  loam,
  @JsonValue('silt')
  silt,
  @JsonValue('peat')
  peat,
}

enum SoilHealth {
  @JsonValue('excellent')
  excellent,
  @JsonValue('good')
  good,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
}

// Extensions
extension FarmTypeExtension on FarmType {
  String get displayName {
    switch (this) {
      case FarmType.organic:
        return 'Organic';
      case FarmType.conventional:
        return 'Conventional';
      case FarmType.mixed:
        return 'Mixed';
    }
  }
}

extension CropTypeExtension on CropType {
  String get displayName {
    switch (this) {
      case CropType.cereal:
        return 'Cereal';
      case CropType.vegetable:
        return 'Vegetable';
      case CropType.fruit:
        return 'Fruit';
      case CropType.legume:
        return 'Legume';
      case CropType.cashCrop:
        return 'Cash Crop';
    }
  }
}

extension CropStageExtension on CropStage {
  String get displayName {
    switch (this) {
      case CropStage.seedling:
        return 'Seedling';
      case CropStage.vegetative:
        return 'Vegetative';
      case CropStage.flowering:
        return 'Flowering';
      case CropStage.fruiting:
        return 'Fruiting';
      case CropStage.maturity:
        return 'Maturity';
      case CropStage.harvested:
        return 'Harvested';
    }
  }
}

extension SoilHealthExtension on SoilHealth {
  String get displayName {
    switch (this) {
      case SoilHealth.excellent:
        return 'Excellent';
      case SoilHealth.good:
        return 'Good';
      case SoilHealth.fair:
        return 'Fair';
      case SoilHealth.poor:
        return 'Poor';
    }
  }
}
