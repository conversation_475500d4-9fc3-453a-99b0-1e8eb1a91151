import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:camera/camera.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../widgets/custom_button.dart';
import '../../models/leaf_scan_model.dart';

// State providers for leaf scanning
final leafScanStateProvider = StateNotifierProvider<LeafScanNotifier, LeafScanState>((ref) {
  return LeafScanNotifier();
});

final availableCamerasProvider = FutureProvider<List<CameraDescription>>((ref) async {
  return await availableCameras();
});

class LeafScanScreen extends ConsumerStatefulWidget {
  const LeafScanScreen({super.key});

  @override
  ConsumerState<LeafScanScreen> createState() => _LeafScanScreenState();
}

class _LeafScanScreenState extends ConsumerState<LeafScanScreen> {
  final ImagePicker _imagePicker = ImagePicker();
  CameraController? _cameraController;
  bool _isCameraInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        _cameraController = CameraController(
          cameras.first,
          ResolutionPreset.high,
          enableAudio: false,
        );

        await _cameraController!.initialize();
        if (mounted) {
          setState(() {
            _isCameraInitialized = true;
          });
        }
      }
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }

  Future<void> _captureImage() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      AppHelpers.showErrorSnackBar(context, 'Camera not ready');
      return;
    }

    try {
      final XFile image = await _cameraController!.takePicture();
      ref.read(leafScanStateProvider.notifier).setImage(File(image.path));

      // Navigate to analysis screen
      _showAnalysisDialog();
    } catch (e) {
      AppHelpers.showErrorSnackBar(context, 'Failed to capture image: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: AppConstants.imageQuality,
        maxWidth: AppConstants.maxImageWidth,
        maxHeight: AppConstants.maxImageHeight,
      );

      if (image != null) {
        ref.read(leafScanStateProvider.notifier).setImage(File(image.path));
        _showAnalysisDialog();
      }
    } catch (e) {
      AppHelpers.showErrorSnackBar(context, 'Failed to select image: $e');
    }
  }

  void _showAnalysisDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const LeafAnalysisDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(leafScanStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.leafScan),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Instructions Card
          Container(
            margin: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                child: Column(
                  children: [
                    const Icon(
                      Icons.eco,
                      size: 48,
                      color: AppColors.secondary,
                    ),
                    const SizedBox(height: AppDimensions.paddingMedium),
                    Text(
                      'Leaf Health Analysis',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppDimensions.paddingSmall),
                    Text(
                      'Capture or select a leaf image for health analysis and disease detection',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Camera Preview
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingMedium),
              child: Card(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                  child: _buildCameraPreview(),
                ),
              ),
            ),
          ),

          // Controls
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingLarge),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildControlButton(
                      icon: Icons.photo_library,
                      label: 'Gallery',
                      onPressed: _pickImageFromGallery,
                    ),
                    _buildCaptureButton(),
                    _buildControlButton(
                      icon: Icons.flip_camera_ios,
                      label: 'Flip',
                      onPressed: _flipCamera,
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.paddingMedium),
                _buildTipsCard(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraPreview() {
    if (!_isCameraInitialized || _cameraController == null) {
      return Container(
        height: 300,
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.white),
              SizedBox(height: AppDimensions.paddingMedium),
              Text(
                'Initializing camera...',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }

    return AspectRatio(
      aspectRatio: _cameraController!.value.aspectRatio,
      child: CameraPreview(_cameraController!),
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTap: _captureImage,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.secondary,
          border: Border.all(color: Colors.white, width: 4),
        ),
        child: const Icon(
          Icons.camera_alt,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.surface,
            border: Border.all(color: AppColors.border),
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(icon, color: AppColors.textPrimary),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildTipsCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.secondary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.secondary.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.lightbulb_outline, color: AppColors.secondary),
          const SizedBox(width: AppDimensions.paddingSmall),
          Expanded(
            child: Text(
              'For best results, ensure good lighting and focus on a single leaf',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.secondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _flipCamera() async {
    if (_cameraController == null) return;

    try {
      final cameras = await availableCameras();
      if (cameras.length > 1) {
        final currentCamera = _cameraController!.description;
        final newCamera = cameras.firstWhere(
          (camera) => camera != currentCamera,
          orElse: () => cameras.first,
        );

        await _cameraController!.dispose();
        _cameraController = CameraController(
          newCamera,
          ResolutionPreset.high,
          enableAudio: false,
        );

        await _cameraController!.initialize();
        if (mounted) {
          setState(() {});
        }
      }
    } catch (e) {
      AppHelpers.showErrorSnackBar(context, 'Failed to flip camera: $e');
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leaf Scanning Tips'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('For accurate analysis:'),
              SizedBox(height: 8),
              Text('• Use good natural lighting'),
              Text('• Focus on a single, clear leaf'),
              Text('• Avoid shadows and reflections'),
              Text('• Keep the leaf flat and visible'),
              Text('• Ensure the entire leaf is in frame'),
              SizedBox(height: 16),
              Text('The AI will analyze:'),
              SizedBox(height: 8),
              Text('• Leaf color and health'),
              Text('• Disease symptoms'),
              Text('• Nutrient deficiencies'),
              Text('• Pest damage'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

// Analysis Dialog
class LeafAnalysisDialog extends ConsumerStatefulWidget {
  const LeafAnalysisDialog({super.key});

  @override
  ConsumerState<LeafAnalysisDialog> createState() => _LeafAnalysisDialogState();
}

class _LeafAnalysisDialogState extends ConsumerState<LeafAnalysisDialog> {
  @override
  void initState() {
    super.initState();
    // Start analysis when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(leafScanStateProvider.notifier).analyzeLeaf();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(leafScanStateProvider);

    return AlertDialog(
      title: const Text('Analyzing Leaf...'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (state.isAnalyzing) ...[
            const CircularProgressIndicator(),
            const SizedBox(height: AppDimensions.paddingMedium),
            const Text('AI is analyzing your leaf image...'),
          ] else if (state.analysisResult != null) ...[
            const Icon(Icons.check_circle, color: AppColors.success, size: 48),
            const SizedBox(height: AppDimensions.paddingMedium),
            const Text('Analysis complete!'),
          ] else if (state.error != null) ...[
            const Icon(Icons.error, color: AppColors.error, size: 48),
            const SizedBox(height: AppDimensions.paddingMedium),
            Text('Error: ${state.error}'),
          ],
        ],
      ),
      actions: [
        if (!state.isAnalyzing) ...[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(leafScanStateProvider.notifier).reset();
            },
            child: const Text('Cancel'),
          ),
          if (state.analysisResult != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Navigate to results screen
                AppHelpers.showSuccessSnackBar(context, 'Analysis completed successfully!');
              },
              child: const Text('View Results'),
            ),
        ],
      ],
    );
  }
}

// State management for leaf scanning
class LeafScanState {
  final File? selectedImage;
  final bool isAnalyzing;
  final String? error;
  final LeafScanModel? analysisResult;

  const LeafScanState({
    this.selectedImage,
    this.isAnalyzing = false,
    this.error,
    this.analysisResult,
  });

  LeafScanState copyWith({
    File? selectedImage,
    bool? isAnalyzing,
    String? error,
    LeafScanModel? analysisResult,
  }) {
    return LeafScanState(
      selectedImage: selectedImage ?? this.selectedImage,
      isAnalyzing: isAnalyzing ?? this.isAnalyzing,
      error: error ?? this.error,
      analysisResult: analysisResult ?? this.analysisResult,
    );
  }
}

class LeafScanNotifier extends StateNotifier<LeafScanState> {
  LeafScanNotifier() : super(const LeafScanState());

  void setImage(File image) {
    state = state.copyWith(selectedImage: image, error: null);
  }

  Future<void> analyzeLeaf() async {
    if (state.selectedImage == null) {
      state = state.copyWith(error: 'No image selected');
      return;
    }

    state = state.copyWith(isAnalyzing: true, error: null);

    try {
      // TODO: Implement actual ML analysis
      // This would typically involve:
      // 1. Preprocessing the image
      // 2. Running ML models for disease detection
      // 3. Color analysis for nutrient deficiencies
      // 4. Generating recommendations

      // Simulate analysis delay
      await Future.delayed(const Duration(seconds: 3));

      // Create mock analysis result
      final analysisResult = LeafScanModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        farmId: 'mock-farm-id',
        cropId: 'mock-crop-id',
        imageUrl: state.selectedImage!.path,
        scanDate: DateTime.now(),
        analysisResults: const LeafAnalysisResults(
          colorAnalysis: LeafColorAnalysis(
            dominantColor: '#4CAF50',
            greenness: 0.75,
            yellowness: 0.15,
            browning: 0.10,
            colorDistribution: {
              'green': 0.75,
              'yellow': 0.15,
              'brown': 0.10,
            },
            healthIndicator: ColorHealthIndicator.healthy,
          ),
          diseases: [],
          deficiencies: [],
          pests: [],
          physicalCondition: LeafPhysicalCondition(
            leafArea: 25.5,
            spotCoverage: 0.05,
            wilting: 0.0,
            damage: 0.05,
            grade: PhysicalConditionGrade.good,
          ),
        ),
        recommendations: [],
        status: ScanStatus.completed,
        confidence: 0.92,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      state = state.copyWith(
        isAnalyzing: false,
        analysisResult: analysisResult,
      );
    } catch (e) {
      state = state.copyWith(
        isAnalyzing: false,
        error: e.toString(),
      );
    }
  }

  void reset() {
    state = const LeafScanState();
  }
}
