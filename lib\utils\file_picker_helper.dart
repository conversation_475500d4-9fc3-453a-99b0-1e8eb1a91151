import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class FilePickerHelper {
  static final ImagePicker _picker = ImagePicker();

  /// Pick an image file with web compatibility
  static Future<File?> pickImage() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        if (kIsWeb) {
          // For web, we need to handle files differently
          // The File constructor works differently on web
          return File(pickedFile.path);
        } else {
          // For mobile platforms
          return File(pickedFile.path);
        }
      }
      return null;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }

  /// Pick an image from camera with web compatibility
  static Future<File?> pickImageFromCamera() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      print('Error picking image from camera: $e');
      return null;
    }
  }

  /// Show image picker options (simplified for web compatibility)
  static Future<File?> showImagePickerOptions(BuildContext context) async {
    // For now, just use gallery picker for both web and mobile
    return await pickImage();
  }

  /// Get image widget that works on both web and mobile
  static Widget getImageWidget(File imageFile, {BoxFit fit = BoxFit.cover}) {
    if (kIsWeb) {
      // On web, use Network image or handle differently
      return Image.network(
        imageFile.path,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[300],
            child: const Icon(
              Icons.error,
              color: Colors.red,
            ),
          );
        },
      );
    } else {
      // On mobile, use File image
      return Image.file(
        imageFile,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[300],
            child: const Icon(
              Icons.error,
              color: Colors.red,
            ),
          );
        },
      );
    }
  }
}
