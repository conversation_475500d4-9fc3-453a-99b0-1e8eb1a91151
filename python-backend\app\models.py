from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    phone_number = Column(String, nullable=True)
    profile_image_url = Column(String, nullable=True)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_email_verified = Column(Boolean, default=False)
    role = Column(String, default="farmer")  # farmer, admin, expert
    preferences = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    farms = relationship("Farm", back_populates="owner")
    soil_tests = relationship("SoilTest", back_populates="user")
    leaf_scans = relationship("LeafScan", back_populates="user")

class Farm(Base):
    __tablename__ = "farms"
    
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    owner_id = Column(String, ForeignKey("users.id"), nullable=False)
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    address = Column(String, nullable=False)
    city = Column(String, nullable=False)
    state = Column(String, nullable=False)
    country = Column(String, nullable=False)
    postal_code = Column(String, nullable=True)
    area = Column(Float, nullable=False)  # in hectares
    area_unit = Column(String, default="hectares")
    farm_type = Column(String, nullable=False)  # organic, conventional, mixed
    is_active = Column(Boolean, default=True)
    image_urls = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    owner = relationship("User", back_populates="farms")
    crops = relationship("Crop", back_populates="farm")
    soil_tests = relationship("SoilTest", back_populates="farm")
    leaf_scans = relationship("LeafScan", back_populates="farm")

class Crop(Base):
    __tablename__ = "crops"
    
    id = Column(String, primary_key=True, index=True)
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)
    name = Column(String, nullable=False)
    variety = Column(String, nullable=False)
    crop_type = Column(String, nullable=False)  # cereal, vegetable, fruit, etc.
    planting_date = Column(DateTime, nullable=False)
    harvest_date = Column(DateTime, nullable=True)
    stage = Column(String, nullable=False)  # seedling, vegetative, flowering, etc.
    area_allocated = Column(Float, nullable=False)
    health_status = Column(String, default="good")
    image_urls = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    farm = relationship("Farm", back_populates="crops")

class SoilTest(Base):
    __tablename__ = "soil_tests"
    
    id = Column(String, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)
    sample_location = Column(String, nullable=False)
    collection_date = Column(DateTime, nullable=False)
    analysis_date = Column(DateTime, nullable=True)
    status = Column(String, default="pending")  # pending, processing, completed, failed
    
    # Soil parameters
    ph_level = Column(Float, nullable=True)
    nitrogen = Column(Float, nullable=True)
    phosphorus = Column(Float, nullable=True)
    potassium = Column(Float, nullable=True)
    organic_matter = Column(Float, nullable=True)
    calcium = Column(Float, nullable=True)
    magnesium = Column(Float, nullable=True)
    sulfur = Column(Float, nullable=True)
    zinc = Column(Float, nullable=True)
    iron = Column(Float, nullable=True)
    manganese = Column(Float, nullable=True)
    copper = Column(Float, nullable=True)
    boron = Column(Float, nullable=True)
    electrical_conductivity = Column(Float, nullable=True)
    soil_texture = Column(String, nullable=True)
    
    # Analysis results
    overall_health = Column(String, nullable=True)
    recommendations = Column(JSON, nullable=True)
    report_image_url = Column(String, nullable=True)
    lab_name = Column(String, nullable=True)
    notes = Column(Text, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="soil_tests")
    farm = relationship("Farm", back_populates="soil_tests")

class LeafScan(Base):
    __tablename__ = "leaf_scans"
    
    id = Column(String, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)
    crop_id = Column(String, nullable=True)
    image_url = Column(String, nullable=False)
    scan_date = Column(DateTime, nullable=False)
    status = Column(String, default="processing")  # processing, completed, failed
    confidence = Column(Float, nullable=True)
    
    # Analysis results
    overall_health = Column(String, nullable=True)
    color_analysis = Column(JSON, nullable=True)
    diseases_detected = Column(JSON, nullable=True)
    deficiencies_detected = Column(JSON, nullable=True)
    pests_detected = Column(JSON, nullable=True)
    physical_condition = Column(JSON, nullable=True)
    recommendations = Column(JSON, nullable=True)
    
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="leaf_scans")
    farm = relationship("Farm", back_populates="leaf_scans")

class Recommendation(Base):
    __tablename__ = "recommendations"
    
    id = Column(String, primary_key=True, index=True)
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)
    crop_id = Column(String, nullable=True)
    recommendation_type = Column(String, nullable=False)  # fertilizer, pesticide, irrigation, etc.
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    priority = Column(String, default="medium")  # low, medium, high, critical
    status = Column(String, default="pending")  # pending, active, completed, cancelled
    source_type = Column(String, nullable=True)  # soil_analysis, leaf_scan, weather, expert
    source_id = Column(String, nullable=True)
    
    # Action details
    actions = Column(JSON, nullable=True)
    scheduled_date = Column(DateTime, nullable=True)
    implemented_date = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class WeatherData(Base):
    __tablename__ = "weather_data"
    
    id = Column(Integer, primary_key=True, index=True)
    farm_id = Column(String, ForeignKey("farms.id"), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    temperature = Column(Float, nullable=False)
    humidity = Column(Float, nullable=False)
    rainfall = Column(Float, default=0.0)
    wind_speed = Column(Float, nullable=False)
    wind_direction = Column(Float, nullable=True)
    pressure = Column(Float, nullable=True)
    condition = Column(String, nullable=False)
    visibility = Column(Float, nullable=True)
    uv_index = Column(Float, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
