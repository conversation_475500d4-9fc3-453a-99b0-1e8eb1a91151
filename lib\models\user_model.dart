import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

// User data model
@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEmailVerified;
  final UserRole role;
  final UserPreferences preferences;
  final List<String> farmIds;

  const UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isEmailVerified = false,
    this.role = UserRole.farmer,
    required this.preferences,
    this.farmIds = const [],
  });

  String get fullName => '$firstName $lastName';

  String get displayName => fullName.trim().isEmpty ? email : fullName;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    UserRole? role,
    UserPreferences? preferences,
    List<String>? farmIds,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      role: role ?? this.role,
      preferences: preferences ?? this.preferences,
      farmIds: farmIds ?? this.farmIds,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName)';
  }
}

@JsonSerializable()
class UserPreferences {
  final String language;
  final String temperatureUnit;
  final String measurementUnit;
  final bool notificationsEnabled;
  final bool locationEnabled;
  final String theme;

  const UserPreferences({
    this.language = 'en',
    this.temperatureUnit = 'celsius',
    this.measurementUnit = 'metric',
    this.notificationsEnabled = true,
    this.locationEnabled = true,
    this.theme = 'light',
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);

  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);

  UserPreferences copyWith({
    String? language,
    String? temperatureUnit,
    String? measurementUnit,
    bool? notificationsEnabled,
    bool? locationEnabled,
    String? theme,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      temperatureUnit: temperatureUnit ?? this.temperatureUnit,
      measurementUnit: measurementUnit ?? this.measurementUnit,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      locationEnabled: locationEnabled ?? this.locationEnabled,
      theme: theme ?? this.theme,
    );
  }
}

enum UserRole {
  @JsonValue('farmer')
  farmer,
  @JsonValue('admin')
  admin,
  @JsonValue('expert')
  expert,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.farmer:
        return 'Farmer';
      case UserRole.admin:
        return 'Administrator';
      case UserRole.expert:
        return 'Agricultural Expert';
    }
  }

  bool get canManageFarms {
    return this == UserRole.farmer || this == UserRole.admin;
  }

  bool get canProvideRecommendations {
    return this == UserRole.expert || this == UserRole.admin;
  }
}
