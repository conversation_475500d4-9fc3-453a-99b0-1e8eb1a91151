import 'package:flutter/material.dart';

// App-wide constants (colors, strings, etc.)

class AppColors {
  // Primary Colors - Agriculture Theme
  static const Color primary = Color(0xFF2E7D32); // Deep Green
  static const Color primaryLight = Color(0xFF4CAF50); // Light Green
  static const Color primaryDark = Color(0xFF1B5E20); // Dark Green
  static const Color primaryVariant = Color(0xFF388E3C); // Medium Green

  // Secondary Colors - Earth Tones
  static const Color secondary = Color(0xFF8BC34A); // Lime Green
  static const Color secondaryLight = Color(0xFFCDDC39); // Light Lime
  static const Color secondaryDark = Color(0xFF689F38); // Dark Lime

  // Accent Colors - Warm Tones
  static const Color accent = Color(0xFFFF9800); // Orange
  static const Color accentLight = Color(0xFFFFC107); // Amber
  static const Color accentDark = Color(0xFFF57C00); // Dark Orange

  // Text Colors
  static const Color textPrimary = Color(0xFF1C1B1F);
  static const Color textSecondary = Color(0xFF49454F);
  static const Color textTertiary = Color(0xFF79747E);
  static const Color textLight = Color(0xFFCAC4D0);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Background Colors
  static const Color background = Color(0xFFFEFBFF);
  static const Color backgroundVariant = Color(0xFFF7F2FA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFE7E0EC);
  static const Color surfaceTint = Color(0xFF6750A4);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF388E3C);

  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFF57C00);

  static const Color error = Color(0xFFE53935);
  static const Color errorLight = Color(0xFFEF5350);
  static const Color errorDark = Color(0xFFD32F2F);

  static const Color info = Color(0xFF1976D2);
  static const Color infoLight = Color(0xFF42A5F5);
  static const Color infoDark = Color(0xFF1565C0);

  // Border and Divider
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFE7E0EC);

  // Soil Health Colors with Gradients
  static const Color soilExcellent = Color(0xFF2E7D32);
  static const Color soilGood = Color(0xFF4CAF50);
  static const Color soilFair = Color(0xFF8BC34A);
  static const Color soilPoor = Color(0xFFFF9800);
  static const Color soilCritical = Color(0xFFE53935);

  // Gradient Definitions
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary],
    stops: [0.0, 1.0],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryLight, secondary],
    stops: [0.0, 1.0],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentLight, accent],
    stops: [0.0, 1.0],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [successLight, success],
    stops: [0.0, 1.0],
  );

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // Card Shadows
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x0F000000),
      blurRadius: 8,
      offset: Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> elevatedShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 12,
      offset: Offset(0, 4),
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> floatingShadow = [
    BoxShadow(
      color: Color(0x26000000),
      blurRadius: 16,
      offset: Offset(0, 6),
      spreadRadius: 0,
    ),
  ];
}

class AppStrings {
  // App Info
  static const String appName = 'Farm Management';
  static const String appVersion = '1.0.0';

  // Authentication
  static const String login = 'Login';
  static const String register = 'Register';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String forgotPassword = 'Forgot Password?';
  static const String dontHaveAccount = "Don't have an account?";
  static const String alreadyHaveAccount = 'Already have an account?';
  static const String signUp = 'Sign Up';
  static const String signIn = 'Sign In';
  static const String logout = 'Logout';

  // Dashboard
  static const String dashboard = 'Dashboard';
  static const String welcome = 'Welcome';
  static const String farmOverview = 'Farm Overview';
  static const String quickActions = 'Quick Actions';
  static const String recentActivity = 'Recent Activity';

  // Features
  static const String soilHealth = 'Soil Health';
  static const String leafScan = 'Leaf Scan';
  static const String recommendations = 'Recommendations';
  static const String weatherInfo = 'Weather Info';
  static const String farmMap = 'Farm Map';

  // Soil Health
  static const String uploadSoilCard = 'Upload Soil Health Card';
  static const String soilAnalysis = 'Soil Analysis';
  static const String soilReport = 'Soil Report';
  static const String takePhoto = 'Take Photo';
  static const String selectFromGallery = 'Select from Gallery';

  // Leaf Scanning
  static const String scanLeaf = 'Scan Leaf';
  static const String leafAnalysis = 'Leaf Analysis';
  static const String leafColorChart = 'Leaf Color Chart';
  static const String captureLeaf = 'Capture Leaf';

  // Recommendations
  static const String fertilizers = 'Fertilizers';
  static const String pesticides = 'Pesticides';
  static const String cropRecommendations = 'Crop Recommendations';
  static const String applicationSchedule = 'Application Schedule';

  // Common
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String update = 'Update';
  static const String submit = 'Submit';
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String success = 'Success';
  static const String retry = 'Retry';
  static const String noData = 'No data available';
  static const String comingSoon = 'Coming Soon';

  // Validation Messages
  static const String fieldRequired = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email';
  static const String passwordTooShort =
      'Password must be at least 6 characters';
  static const String passwordsDoNotMatch = 'Passwords do not match';
}

class AppDimensions {
  // Spacing System - 8pt Grid
  static const double space4 = 4.0;
  static const double space8 = 8.0;
  static const double space12 = 12.0;
  static const double space16 = 16.0;
  static const double space20 = 20.0;
  static const double space24 = 24.0;
  static const double space32 = 32.0;
  static const double space40 = 40.0;
  static const double space48 = 48.0;
  static const double space56 = 56.0;
  static const double space64 = 64.0;

  // Padding and Margins
  static const double paddingXSmall = 4.0;
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  static const double paddingXXLarge = 48.0;

  // Border Radius
  static const double radiusXSmall = 4.0;
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  static const double radiusXXLarge = 32.0;
  static const double radiusCircular = 50.0;

  // Icon Sizes
  static const double iconXSmall = 12.0;
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  static const double iconXXLarge = 64.0;

  // Button Dimensions
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeight = 48.0;
  static const double buttonHeightLarge = 56.0;
  static const double buttonHeightXLarge = 64.0;
  static const double buttonMinWidth = 88.0;

  // Card Dimensions
  static const double cardElevation = 1.0;
  static const double cardElevationHover = 3.0;
  static const double cardElevationPressed = 6.0;
  static const double cardRadius = 16.0;
  static const double cardRadiusSmall = 12.0;
  static const double cardRadiusLarge = 20.0;

  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;

  // Bottom Navigation
  static const double bottomNavHeight = 80.0;
  static const double bottomNavElevation = 8.0;

  // List Items
  static const double listItemHeight = 72.0;
  static const double listItemHeightSmall = 56.0;
  static const double listItemHeightLarge = 88.0;

  // Dividers
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 16.0;

  // Image Dimensions
  static const double avatarSmall = 32.0;
  static const double avatarMedium = 48.0;
  static const double avatarLarge = 64.0;
  static const double avatarXLarge = 96.0;

  // Feature Card Dimensions
  static const double featureCardHeight = 120.0;
  static const double featureCardWidth = 160.0;
  static const double featureIconSize = 40.0;
}

class AppAnimations {
  // Duration Constants
  static const Duration durationShort = Duration(milliseconds: 150);
  static const Duration durationMedium = Duration(milliseconds: 300);
  static const Duration durationLong = Duration(milliseconds: 500);
  static const Duration durationXLong = Duration(milliseconds: 800);

  // Curve Constants
  static const Curve curveStandard = Curves.easeInOut;
  static const Curve curveDecelerate = Curves.easeOut;
  static const Curve curveAccelerate = Curves.easeIn;
  static const Curve curveBounce = Curves.bounceOut;
  static const Curve curveElastic = Curves.elasticOut;

  // Page Transition Duration
  static const Duration pageTransition = Duration(milliseconds: 300);

  // Loading Animation
  static const Duration loadingRotation = Duration(milliseconds: 1000);

  // Shimmer Animation
  static const Duration shimmerDuration = Duration(milliseconds: 1500);
}

class AppConstants {
  // API Configuration - Python FastAPI Backend
  static const String baseUrl = 'http://localhost:8000/api';
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String farmDataKey = 'farm_data';
  static const String settingsKey = 'app_settings';

  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int imageQuality = 85;
  static const double maxImageWidth = 1920;
  static const double maxImageHeight = 1080;

  // Location Configuration
  static const double locationAccuracy = 100.0; // meters
  static const int locationTimeout = 30; // seconds

  // Soil Health Thresholds
  static const double soilPhMin = 6.0;
  static const double soilPhMax = 7.5;
  static const double nitrogenMin = 280.0;
  static const double phosphorusMin = 11.0;
  static const double potassiumMin = 120.0;
}
