#!/usr/bin/env python3
"""
Farm Management API Server
Run this script to start the development server
"""

import uvicorn
import os
import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """Main function to run the server"""
    # Create uploads directory if it doesn't exist
    uploads_dir = current_dir / "uploads"
    uploads_dir.mkdir(exist_ok=True)
    
    print("🚀 Starting Farm Management API Server...")
    print("📁 Upload directory:", uploads_dir)
    print("🌐 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔧 Alternative docs: http://localhost:8000/redoc")
    print("\n" + "="*50)
    
    # Run the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    main()
