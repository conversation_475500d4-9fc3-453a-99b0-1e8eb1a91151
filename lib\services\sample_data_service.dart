import 'dart:math';
import '../services/farm_service.dart';
import '../services/soil_service.dart';
import '../models/sqlite_user_model.dart';

class SampleDataService {
  final FarmService _farmService = FarmService();
  final SoilService _soilService = SoilService();

  // Generate unique ID
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return '${timestamp}_$random';
  }

  // Create sample farm for a user
  Future<void> createSampleFarm(SqliteUser user) async {
    try {
      // Check if user already has farms
      final existingFarms = await _farmService.getFarmsForUser(user.id);
      if (existingFarms.isNotEmpty) {
        return; // User already has farms
      }

      // Create a sample farm
      final farm = await _farmService.createFarm(
        userId: user.id,
        name: 'My First Farm',
        description: 'A sample farm to get you started with Smart Precision Agriculture',
        latitude: 40.7128, // New York coordinates as example
        longitude: -74.0060,
        address: '123 Farm Road',
        city: 'Farmville',
        state: 'NY',
        country: 'USA',
        postalCode: '12345',
        area: 25.5,
        areaUnit: 'hectares',
        farmType: 'Mixed Farming',
      );

      // Create sample crops for the farm
      await _farmService.createCrop(
        farmId: farm.id,
        name: 'Corn',
        variety: 'Sweet Corn',
        cropType: 'Cereal',
        plantingDate: DateTime.now().subtract(const Duration(days: 45)),
        harvestDate: DateTime.now().add(const Duration(days: 75)),
        stage: 'Growing',
        areaAllocated: 10.0,
        healthStatus: 'good',
      );

      await _farmService.createCrop(
        farmId: farm.id,
        name: 'Tomatoes',
        variety: 'Cherry Tomatoes',
        cropType: 'Vegetable',
        plantingDate: DateTime.now().subtract(const Duration(days: 30)),
        harvestDate: DateTime.now().add(const Duration(days: 60)),
        stage: 'Flowering',
        areaAllocated: 8.0,
        healthStatus: 'good',
      );

      await _farmService.createCrop(
        farmId: farm.id,
        name: 'Wheat',
        variety: 'Winter Wheat',
        cropType: 'Cereal',
        plantingDate: DateTime.now().subtract(const Duration(days: 60)),
        harvestDate: DateTime.now().add(const Duration(days: 90)),
        stage: 'Maturation',
        areaAllocated: 7.5,
        healthStatus: 'excellent',
      );

      // Create a sample soil test
      final soilTest = await _soilService.createSoilTest(
        userId: user.id,
        farmId: farm.id,
        sampleLocation: 'North Field - Section A',
        collectionDate: DateTime.now().subtract(const Duration(days: 7)),
        labName: 'AgriTest Labs',
        notes: 'Sample soil test for demonstration purposes',
      );

      // Analyze the soil test
      await _soilService.analyzeSoilTest(soilTest.id);

    } catch (e) {
      // Ignore errors in sample data creation
      print('Error creating sample data: $e');
    }
  }

  // Create sample data for demonstration
  Future<void> createDemoData(SqliteUser user) async {
    try {
      final farms = await _farmService.getFarmsForUser(user.id);
      if (farms.isEmpty) {
        await createSampleFarm(user);
        return;
      }

      final farm = farms.first;

      // Create additional sample soil tests
      final sampleLocations = [
        'South Field - Section B',
        'East Field - Section C',
        'West Field - Section D',
        'Central Field - Section E',
      ];

      for (int i = 0; i < sampleLocations.length; i++) {
        final daysAgo = (i + 1) * 14; // 14, 28, 42, 56 days ago
        
        final soilTest = await _soilService.createSoilTest(
          userId: user.id,
          farmId: farm.id,
          sampleLocation: sampleLocations[i],
          collectionDate: DateTime.now().subtract(Duration(days: daysAgo)),
          labName: i % 2 == 0 ? 'AgriTest Labs' : 'SoilPro Analytics',
          notes: 'Historical soil test data for trend analysis',
        );

        // Analyze some of the tests
        if (i < 3) {
          await _soilService.analyzeSoilTest(soilTest.id);
        }
      }

      // Create additional crops
      final additionalCrops = [
        {
          'name': 'Soybeans',
          'variety': 'Edamame',
          'cropType': 'Legume',
          'daysPlanted': 20,
          'daysToHarvest': 100,
          'stage': 'Vegetative',
          'area': 5.0,
          'health': 'good',
        },
        {
          'name': 'Carrots',
          'variety': 'Baby Carrots',
          'cropType': 'Root Vegetable',
          'daysPlanted': 35,
          'daysToHarvest': 45,
          'stage': 'Root Development',
          'area': 3.0,
          'health': 'excellent',
        },
      ];

      for (final cropData in additionalCrops) {
        await _farmService.createCrop(
          farmId: farm.id,
          name: cropData['name'] as String,
          variety: cropData['variety'] as String,
          cropType: cropData['cropType'] as String,
          plantingDate: DateTime.now().subtract(Duration(days: cropData['daysPlanted'] as int)),
          harvestDate: DateTime.now().add(Duration(days: cropData['daysToHarvest'] as int)),
          stage: cropData['stage'] as String,
          areaAllocated: cropData['area'] as double,
          healthStatus: cropData['health'] as String,
        );
      }

    } catch (e) {
      print('Error creating demo data: $e');
    }
  }

  // Get farm statistics summary
  Future<Map<String, dynamic>> getFarmSummary(String userId) async {
    try {
      final farms = await _farmService.getFarmsForUser(userId);
      if (farms.isEmpty) {
        return {
          'totalFarms': 0,
          'totalArea': 0.0,
          'totalCrops': 0,
          'totalSoilTests': 0,
        };
      }

      double totalArea = 0.0;
      int totalCrops = 0;
      int totalSoilTests = 0;

      for (final farm in farms) {
        totalArea += farm.area;
        
        final crops = await _farmService.getCropsForFarm(farm.id);
        totalCrops += crops.length;
        
        final soilTests = await _soilService.getSoilTestsForFarm(farm.id);
        totalSoilTests += soilTests.length;
      }

      return {
        'totalFarms': farms.length,
        'totalArea': totalArea,
        'totalCrops': totalCrops,
        'totalSoilTests': totalSoilTests,
        'farms': farms,
      };
    } catch (e) {
      return {
        'totalFarms': 0,
        'totalArea': 0.0,
        'totalCrops': 0,
        'totalSoilTests': 0,
        'error': e.toString(),
      };
    }
  }

  // Get recent activity
  Future<List<Map<String, dynamic>>> getRecentActivity(String userId) async {
    try {
      final activities = <Map<String, dynamic>>[];
      
      // Get recent soil tests
      final soilTests = await _soilService.getSoilTestsForUser(userId);
      for (final test in soilTests.take(5)) {
        activities.add({
          'type': 'soil_test',
          'title': 'Soil Test - ${test.sampleLocation}',
          'subtitle': 'Status: ${test.status}',
          'date': test.createdAt,
          'icon': 'landscape',
          'status': test.status,
        });
      }

      // Get farms
      final farms = await _farmService.getFarmsForUser(userId);
      for (final farm in farms) {
        // Get recent crops
        final crops = await _farmService.getCropsForFarm(farm.id);
        for (final crop in crops.take(3)) {
          activities.add({
            'type': 'crop',
            'title': '${crop.name} - ${crop.variety}',
            'subtitle': 'Stage: ${crop.stage}',
            'date': crop.createdAt,
            'icon': 'eco',
            'status': crop.healthStatus,
          });
        }
      }

      // Sort by date (most recent first)
      activities.sort((a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));
      
      return activities.take(10).toList();
    } catch (e) {
      return [];
    }
  }
}
