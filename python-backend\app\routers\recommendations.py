from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import uuid

from ..database import get_db
from ..auth import get_current_active_user
from ..models import User, Farm, Recommendation
from ..schemas import RecommendationCreate, RecommendationUpdate, RecommendationResponse

router = APIRouter()

@router.post("/", response_model=RecommendationResponse)
async def create_recommendation(
    recommendation: RecommendationCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new recommendation"""
    # Verify farm ownership
    farm = db.query(Farm).filter(Farm.id == recommendation.farm_id).first()
    if not farm:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Farm not found"
        )
    
    if farm.owner_id != current_user.id and current_user.role not in ["expert", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        recommendation_id = str(uuid.uuid4())
        
        db_recommendation = Recommendation(
            id=recommendation_id,
            farm_id=recommendation.farm_id,
            crop_id=recommendation.crop_id,
            recommendation_type=recommendation.recommendation_type.value,
            title=recommendation.title,
            description=recommendation.description,
            priority=recommendation.priority.value,
            actions=recommendation.actions,
            scheduled_date=recommendation.scheduled_date,
            status="pending"
        )
        
        db.add(db_recommendation)
        db.commit()
        db.refresh(db_recommendation)
        
        return RecommendationResponse.from_orm(db_recommendation)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create recommendation: {str(e)}"
        )

@router.get("/", response_model=List[RecommendationResponse])
async def list_recommendations(
    farm_id: str = None,
    status_filter: str = None,
    priority: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get recommendations for user's farms"""
    # Base query - get recommendations for user's farms
    user_farm_ids = [farm.id for farm in db.query(Farm).filter(Farm.owner_id == current_user.id).all()]
    
    if not user_farm_ids:
        return []
    
    query = db.query(Recommendation).filter(Recommendation.farm_id.in_(user_farm_ids))
    
    # Apply filters
    if farm_id:
        if farm_id not in user_farm_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        query = query.filter(Recommendation.farm_id == farm_id)
    
    if status_filter:
        query = query.filter(Recommendation.status == status_filter)
    
    if priority:
        query = query.filter(Recommendation.priority == priority)
    
    recommendations = query.order_by(
        Recommendation.priority.desc(),
        Recommendation.created_at.desc()
    ).all()
    
    return [RecommendationResponse.from_orm(rec) for rec in recommendations]

@router.get("/{recommendation_id}", response_model=RecommendationResponse)
async def get_recommendation(
    recommendation_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get recommendation by ID"""
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    
    if not recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    # Check if user owns the farm
    farm = db.query(Farm).filter(Farm.id == recommendation.farm_id).first()
    if not farm or farm.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return RecommendationResponse.from_orm(recommendation)

@router.put("/{recommendation_id}", response_model=RecommendationResponse)
async def update_recommendation(
    recommendation_id: str,
    recommendation_update: RecommendationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update recommendation"""
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    
    if not recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    # Check permissions
    farm = db.query(Farm).filter(Farm.id == recommendation.farm_id).first()
    if not farm or (farm.owner_id != current_user.id and current_user.role not in ["expert", "admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        # Update recommendation fields
        update_data = recommendation_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(recommendation, field):
                if field == "priority" and value:
                    setattr(recommendation, field, value.value)
                else:
                    setattr(recommendation, field, value)
        
        db.commit()
        db.refresh(recommendation)
        
        return RecommendationResponse.from_orm(recommendation)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update recommendation: {str(e)}"
        )

@router.delete("/{recommendation_id}")
async def delete_recommendation(
    recommendation_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete recommendation"""
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    
    if not recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    # Check permissions
    farm = db.query(Farm).filter(Farm.id == recommendation.farm_id).first()
    if not farm or (farm.owner_id != current_user.id and current_user.role not in ["expert", "admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        db.delete(recommendation)
        db.commit()
        
        return {"message": "Recommendation deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete recommendation: {str(e)}"
        )

@router.post("/{recommendation_id}/implement")
async def implement_recommendation(
    recommendation_id: str,
    notes: str = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark recommendation as implemented"""
    recommendation = db.query(Recommendation).filter(Recommendation.id == recommendation_id).first()
    
    if not recommendation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recommendation not found"
        )
    
    # Check if user owns the farm
    farm = db.query(Farm).filter(Farm.id == recommendation.farm_id).first()
    if not farm or farm.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    try:
        from datetime import datetime
        recommendation.status = "completed"
        recommendation.implemented_date = datetime.utcnow()
        if notes:
            recommendation.notes = notes
        
        db.commit()
        db.refresh(recommendation)
        
        return {"message": "Recommendation marked as implemented"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to implement recommendation: {str(e)}"
        )
