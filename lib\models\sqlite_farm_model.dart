import 'dart:convert';

class SqliteFarm {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String country;
  final String? postalCode;
  final double area;
  final String areaUnit;
  final String farmType;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  SqliteFarm({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    this.postalCode,
    required this.area,
    this.areaUnit = 'hectares',
    required this.farmType,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  String get fullAddress => '$address, $city, $state, $country';

  String get areaDisplay => '${area.toStringAsFixed(2)} $areaUnit';

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'area': area,
      'area_unit': areaUnit,
      'farm_type': farmType,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory SqliteFarm.fromMap(Map<String, dynamic> map) {
    return SqliteFarm(
      id: map['id'] ?? '',
      userId: map['user_id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      latitude: (map['latitude'] ?? 0.0).toDouble(),
      longitude: (map['longitude'] ?? 0.0).toDouble(),
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      country: map['country'] ?? '',
      postalCode: map['postal_code'],
      area: (map['area'] ?? 0.0).toDouble(),
      areaUnit: map['area_unit'] ?? 'hectares',
      farmType: map['farm_type'] ?? '',
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory SqliteFarm.fromJson(String source) => SqliteFarm.fromMap(json.decode(source));

  SqliteFarm copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    double? area,
    String? areaUnit,
    String? farmType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SqliteFarm(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      area: area ?? this.area,
      areaUnit: areaUnit ?? this.areaUnit,
      farmType: farmType ?? this.farmType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SqliteFarm(id: $id, userId: $userId, name: $name, description: $description, latitude: $latitude, longitude: $longitude, address: $address, city: $city, state: $state, country: $country, postalCode: $postalCode, area: $area, areaUnit: $areaUnit, farmType: $farmType, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SqliteFarm &&
        other.id == id &&
        other.userId == userId &&
        other.name == name &&
        other.description == description &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.address == address &&
        other.city == city &&
        other.state == state &&
        other.country == country &&
        other.postalCode == postalCode &&
        other.area == area &&
        other.areaUnit == areaUnit &&
        other.farmType == farmType &&
        other.isActive == isActive &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        name.hashCode ^
        description.hashCode ^
        latitude.hashCode ^
        longitude.hashCode ^
        address.hashCode ^
        city.hashCode ^
        state.hashCode ^
        country.hashCode ^
        postalCode.hashCode ^
        area.hashCode ^
        areaUnit.hashCode ^
        farmType.hashCode ^
        isActive.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}

class SqliteCrop {
  final String id;
  final String farmId;
  final String name;
  final String variety;
  final String cropType;
  final DateTime plantingDate;
  final DateTime? harvestDate;
  final String stage;
  final double areaAllocated;
  final String healthStatus;
  final DateTime createdAt;
  final DateTime? updatedAt;

  SqliteCrop({
    required this.id,
    required this.farmId,
    required this.name,
    required this.variety,
    required this.cropType,
    required this.plantingDate,
    this.harvestDate,
    required this.stage,
    required this.areaAllocated,
    this.healthStatus = 'good',
    required this.createdAt,
    this.updatedAt,
  });

  String get displayName => '$name ($variety)';

  int get daysFromPlanting => DateTime.now().difference(plantingDate).inDays;

  int? get daysToHarvest => harvestDate?.difference(DateTime.now()).inDays;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'farm_id': farmId,
      'name': name,
      'variety': variety,
      'crop_type': cropType,
      'planting_date': plantingDate.toIso8601String(),
      'harvest_date': harvestDate?.toIso8601String(),
      'stage': stage,
      'area_allocated': areaAllocated,
      'health_status': healthStatus,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory SqliteCrop.fromMap(Map<String, dynamic> map) {
    return SqliteCrop(
      id: map['id'] ?? '',
      farmId: map['farm_id'] ?? '',
      name: map['name'] ?? '',
      variety: map['variety'] ?? '',
      cropType: map['crop_type'] ?? '',
      plantingDate: DateTime.parse(map['planting_date']),
      harvestDate: map['harvest_date'] != null ? DateTime.parse(map['harvest_date']) : null,
      stage: map['stage'] ?? '',
      areaAllocated: (map['area_allocated'] ?? 0.0).toDouble(),
      healthStatus: map['health_status'] ?? 'good',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory SqliteCrop.fromJson(String source) => SqliteCrop.fromMap(json.decode(source));

  SqliteCrop copyWith({
    String? id,
    String? farmId,
    String? name,
    String? variety,
    String? cropType,
    DateTime? plantingDate,
    DateTime? harvestDate,
    String? stage,
    double? areaAllocated,
    String? healthStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SqliteCrop(
      id: id ?? this.id,
      farmId: farmId ?? this.farmId,
      name: name ?? this.name,
      variety: variety ?? this.variety,
      cropType: cropType ?? this.cropType,
      plantingDate: plantingDate ?? this.plantingDate,
      harvestDate: harvestDate ?? this.harvestDate,
      stage: stage ?? this.stage,
      areaAllocated: areaAllocated ?? this.areaAllocated,
      healthStatus: healthStatus ?? this.healthStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SqliteCrop(id: $id, farmId: $farmId, name: $name, variety: $variety, cropType: $cropType, plantingDate: $plantingDate, harvestDate: $harvestDate, stage: $stage, areaAllocated: $areaAllocated, healthStatus: $healthStatus, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SqliteCrop &&
        other.id == id &&
        other.farmId == farmId &&
        other.name == name &&
        other.variety == variety &&
        other.cropType == cropType &&
        other.plantingDate == plantingDate &&
        other.harvestDate == harvestDate &&
        other.stage == stage &&
        other.areaAllocated == areaAllocated &&
        other.healthStatus == healthStatus &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        farmId.hashCode ^
        name.hashCode ^
        variety.hashCode ^
        cropType.hashCode ^
        plantingDate.hashCode ^
        harvestDate.hashCode ^
        stage.hashCode ^
        areaAllocated.hashCode ^
        healthStatus.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
