from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

# Enums
class UserRole(str, Enum):
    farmer = "farmer"
    admin = "admin"
    expert = "expert"

class FarmType(str, Enum):
    organic = "organic"
    conventional = "conventional"
    mixed = "mixed"

class CropType(str, Enum):
    cereal = "cereal"
    vegetable = "vegetable"
    fruit = "fruit"
    legume = "legume"
    cash_crop = "cash_crop"

class CropStage(str, Enum):
    seedling = "seedling"
    vegetative = "vegetative"
    flowering = "flowering"
    fruiting = "fruiting"
    maturity = "maturity"
    harvested = "harvested"

class SoilHealthGrade(str, Enum):
    excellent = "excellent"
    good = "good"
    fair = "fair"
    poor = "poor"
    unknown = "unknown"

class RecommendationType(str, Enum):
    fertilizer = "fertilizer"
    pesticide = "pesticide"
    irrigation = "irrigation"
    soil_amendment = "soil_amendment"
    crop_management = "crop_management"

class RecommendationPriority(str, Enum):
    low = "low"
    medium = "medium"
    high = "high"
    critical = "critical"

# Base schemas
class UserBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    phone_number: Optional[str] = None
    role: UserRole = UserRole.farmer

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
    profile_image_url: Optional[str] = None

class UserResponse(UserBase):
    id: str
    profile_image_url: Optional[str] = None
    is_active: bool
    is_email_verified: bool
    preferences: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: UserResponse

class TokenData(BaseModel):
    email: Optional[str] = None

# Farm schemas
class FarmLocation(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    address: str
    city: str
    state: str
    country: str
    postal_code: Optional[str] = None

class FarmBase(BaseModel):
    name: str
    description: Optional[str] = None
    location: FarmLocation
    area: float = Field(..., gt=0)
    area_unit: str = "hectares"
    farm_type: FarmType

class FarmCreate(FarmBase):
    pass

class FarmUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    area: Optional[float] = Field(None, gt=0)
    farm_type: Optional[FarmType] = None

class FarmResponse(FarmBase):
    id: str
    owner_id: str
    is_active: bool
    image_urls: Optional[List[str]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Crop schemas
class CropBase(BaseModel):
    name: str
    variety: str
    crop_type: CropType
    planting_date: datetime
    harvest_date: Optional[datetime] = None
    stage: CropStage
    area_allocated: float = Field(..., gt=0)

class CropCreate(CropBase):
    farm_id: str

class CropUpdate(BaseModel):
    name: Optional[str] = None
    variety: Optional[str] = None
    stage: Optional[CropStage] = None
    harvest_date: Optional[datetime] = None
    health_status: Optional[str] = None

class CropResponse(CropBase):
    id: str
    farm_id: str
    health_status: str
    image_urls: Optional[List[str]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Soil test schemas
class SoilTestBase(BaseModel):
    sample_location: str
    collection_date: datetime
    notes: Optional[str] = None

class SoilTestCreate(SoilTestBase):
    farm_id: str

class SoilParameterUpdate(BaseModel):
    ph_level: Optional[float] = None
    nitrogen: Optional[float] = None
    phosphorus: Optional[float] = None
    potassium: Optional[float] = None
    organic_matter: Optional[float] = None
    calcium: Optional[float] = None
    magnesium: Optional[float] = None
    sulfur: Optional[float] = None
    zinc: Optional[float] = None
    iron: Optional[float] = None
    manganese: Optional[float] = None
    copper: Optional[float] = None
    boron: Optional[float] = None
    electrical_conductivity: Optional[float] = None
    soil_texture: Optional[str] = None

class SoilTestUpdate(SoilParameterUpdate):
    analysis_date: Optional[datetime] = None
    status: Optional[str] = None
    overall_health: Optional[SoilHealthGrade] = None
    recommendations: Optional[Dict[str, Any]] = None
    lab_name: Optional[str] = None
    notes: Optional[str] = None

class SoilTestResponse(SoilTestBase):
    id: str
    user_id: str
    farm_id: str
    analysis_date: Optional[datetime] = None
    status: str
    ph_level: Optional[float] = None
    nitrogen: Optional[float] = None
    phosphorus: Optional[float] = None
    potassium: Optional[float] = None
    organic_matter: Optional[float] = None
    overall_health: Optional[str] = None
    recommendations: Optional[Dict[str, Any]] = None
    report_image_url: Optional[str] = None
    lab_name: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Leaf scan schemas
class LeafScanCreate(BaseModel):
    farm_id: str
    crop_id: Optional[str] = None

class LeafScanUpdate(BaseModel):
    status: Optional[str] = None
    confidence: Optional[float] = None
    overall_health: Optional[str] = None
    color_analysis: Optional[Dict[str, Any]] = None
    diseases_detected: Optional[List[Dict[str, Any]]] = None
    deficiencies_detected: Optional[List[Dict[str, Any]]] = None
    pests_detected: Optional[List[Dict[str, Any]]] = None
    physical_condition: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[Dict[str, Any]]] = None

class LeafScanResponse(BaseModel):
    id: str
    user_id: str
    farm_id: str
    crop_id: Optional[str] = None
    image_url: str
    scan_date: datetime
    status: str
    confidence: Optional[float] = None
    overall_health: Optional[str] = None
    color_analysis: Optional[Dict[str, Any]] = None
    diseases_detected: Optional[List[Dict[str, Any]]] = None
    deficiencies_detected: Optional[List[Dict[str, Any]]] = None
    pests_detected: Optional[List[Dict[str, Any]]] = None
    physical_condition: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[Dict[str, Any]]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Recommendation schemas
class RecommendationBase(BaseModel):
    title: str
    description: str
    recommendation_type: RecommendationType
    priority: RecommendationPriority = RecommendationPriority.medium

class RecommendationCreate(RecommendationBase):
    farm_id: str
    crop_id: Optional[str] = None
    actions: Optional[List[Dict[str, Any]]] = None
    scheduled_date: Optional[datetime] = None

class RecommendationUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[RecommendationPriority] = None
    status: Optional[str] = None
    scheduled_date: Optional[datetime] = None
    implemented_date: Optional[datetime] = None
    notes: Optional[str] = None

class RecommendationResponse(RecommendationBase):
    id: str
    farm_id: str
    crop_id: Optional[str] = None
    status: str
    source_type: Optional[str] = None
    source_id: Optional[str] = None
    actions: Optional[List[Dict[str, Any]]] = None
    scheduled_date: Optional[datetime] = None
    implemented_date: Optional[datetime] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Weather schemas
class WeatherResponse(BaseModel):
    temperature: float
    humidity: float
    rainfall: float
    wind_speed: float
    condition: str
    timestamp: datetime

    class Config:
        from_attributes = True
